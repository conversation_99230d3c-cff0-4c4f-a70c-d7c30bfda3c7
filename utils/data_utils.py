"""
数据处理工具函数
"""
import numpy as np
import cv2
import pickle
import os
from typing import List, Dict, Tuple, Optional
import matplotlib.pyplot as plt
from config import Config

def normalize_depth_image(depth_image: np.ndarray, 
                         min_depth: float = None, 
                         max_depth: float = None) -> np.ndarray:
    """
    归一化深度图像
    
    Args:
        depth_image: 深度图像
        min_depth: 最小深度值
        max_depth: 最大深度值
        
    Returns:
        normalized_image: 归一化后的深度图像
    """
    if min_depth is None:
        min_depth = Config.POINTCLOUD_MIN_DISTANCE
    if max_depth is None:
        max_depth = Config.POINTCLOUD_MAX_DISTANCE
    
    # 裁剪到有效范围
    clipped = np.clip(depth_image, min_depth, max_depth)
    
    # 归一化到[0, 1]
    normalized = (clipped - min_depth) / (max_depth - min_depth)
    
    return normalized

def resize_image(image: np.ndarray, target_size: Tuple[int, int]) -> np.ndarray:
    """
    调整图像尺寸
    
    Args:
        image: 输入图像
        target_size: 目标尺寸 (width, height)
        
    Returns:
        resized_image: 调整后的图像
    """
    return cv2.resize(image, target_size, interpolation=cv2.INTER_LINEAR)

def augment_depth_image(depth_image: np.ndarray, 
                       noise_std: float = 0.01,
                       brightness_range: Tuple[float, float] = (0.8, 1.2)) -> np.ndarray:
    """
    深度图像数据增强
    
    Args:
        depth_image: 深度图像
        noise_std: 噪声标准差
        brightness_range: 亮度调整范围
        
    Returns:
        augmented_image: 增强后的图像
    """
    augmented = depth_image.copy()
    
    # 添加高斯噪声
    noise = np.random.normal(0, noise_std, depth_image.shape)
    augmented += noise
    
    # 亮度调整
    brightness_factor = np.random.uniform(brightness_range[0], brightness_range[1])
    augmented *= brightness_factor
    
    # 确保值在有效范围内
    augmented = np.clip(augmented, Config.POINTCLOUD_MIN_DISTANCE, Config.POINTCLOUD_MAX_DISTANCE)
    
    return augmented

def filter_outliers(points: np.ndarray, 
                   method: str = 'statistical',
                   nb_neighbors: int = 20,
                   std_ratio: float = 2.0) -> np.ndarray:
    """
    过滤点云异常值
    
    Args:
        points: 点云数据 (N, 3)
        method: 过滤方法 ('statistical' 或 'radius')
        nb_neighbors: 邻居数量
        std_ratio: 标准差比率
        
    Returns:
        filtered_points: 过滤后的点云
    """
    if len(points) == 0:
        return points
    
    if method == 'statistical':
        # 统计异常值过滤
        distances = []
        for i, point in enumerate(points):
            # 计算到其他点的距离
            dists = np.linalg.norm(points - point, axis=1)
            # 排除自身
            dists = np.sort(dists)[1:nb_neighbors+1]
            distances.append(np.mean(dists))
        
        distances = np.array(distances)
        mean_dist = np.mean(distances)
        std_dist = np.std(distances)
        
        # 过滤异常值
        valid_mask = distances < (mean_dist + std_ratio * std_dist)
        return points[valid_mask]
    
    else:
        # 简单返回原始点云
        return points

def compute_point_features(points: np.ndarray) -> Dict:
    """
    计算点云特征
    
    Args:
        points: 点云数据 (N, 3)
        
    Returns:
        features: 特征字典
    """
    if len(points) == 0:
        return {
            'num_points': 0,
            'centroid': np.zeros(3),
            'bounding_box': np.zeros((2, 3)),
            'density': 0.0
        }
    
    features = {
        'num_points': len(points),
        'centroid': np.mean(points, axis=0),
        'bounding_box': np.array([np.min(points, axis=0), np.max(points, axis=0)]),
        'density': len(points) / (np.prod(np.max(points, axis=0) - np.min(points, axis=0)) + 1e-6)
    }
    
    return features

def split_dataset(dataset: List[Dict], 
                 train_ratio: float = 0.8,
                 val_ratio: float = 0.1,
                 test_ratio: float = 0.1,
                 random_seed: int = 42) -> Tuple[List[Dict], List[Dict], List[Dict]]:
    """
    划分数据集
    
    Args:
        dataset: 原始数据集
        train_ratio: 训练集比例
        val_ratio: 验证集比例
        test_ratio: 测试集比例
        random_seed: 随机种子
        
    Returns:
        train_set, val_set, test_set: 划分后的数据集
    """
    assert abs(train_ratio + val_ratio + test_ratio - 1.0) < 1e-6, "比例之和必须为1"
    
    np.random.seed(random_seed)
    
    num_samples = len(dataset)
    indices = np.random.permutation(num_samples)
    
    num_train = int(num_samples * train_ratio)
    num_val = int(num_samples * val_ratio)
    
    train_indices = indices[:num_train]
    val_indices = indices[num_train:num_train + num_val]
    test_indices = indices[num_train + num_val:]
    
    train_set = [dataset[i] for i in train_indices]
    val_set = [dataset[i] for i in val_indices]
    test_set = [dataset[i] for i in test_indices]
    
    return train_set, val_set, test_set

def save_dataset_split(train_set: List[Dict], 
                      val_set: List[Dict], 
                      test_set: List[Dict],
                      base_filename: str = "dataset"):
    """
    保存划分后的数据集
    
    Args:
        train_set: 训练集
        val_set: 验证集
        test_set: 测试集
        base_filename: 基础文件名
    """
    Config.create_directories()
    
    datasets = {
        'train': train_set,
        'val': val_set,
        'test': test_set
    }
    
    for split_name, split_data in datasets.items():
        filename = f"{base_filename}_{split_name}.pkl"
        filepath = os.path.join(Config.DATA_DIR, filename)
        
        with open(filepath, 'wb') as f:
            pickle.dump(split_data, f)
        
        print(f"{split_name}集已保存: {filepath} ({len(split_data)} 样本)")

def load_dataset_split(base_filename: str = "dataset") -> Tuple[List[Dict], List[Dict], List[Dict]]:
    """
    加载划分后的数据集
    
    Args:
        base_filename: 基础文件名
        
    Returns:
        train_set, val_set, test_set: 加载的数据集
    """
    datasets = {}
    
    for split_name in ['train', 'val', 'test']:
        filename = f"{base_filename}_{split_name}.pkl"
        filepath = os.path.join(Config.DATA_DIR, filename)
        
        if os.path.exists(filepath):
            with open(filepath, 'rb') as f:
                datasets[split_name] = pickle.load(f)
            print(f"{split_name}集已加载: {len(datasets[split_name])} 样本")
        else:
            print(f"警告: 未找到{split_name}集文件: {filepath}")
            datasets[split_name] = []
    
    return datasets['train'], datasets['val'], datasets['test']

def visualize_dataset_statistics(dataset: List[Dict], save_path: str = None):
    """
    可视化数据集统计信息
    
    Args:
        dataset: 数据集
        save_path: 保存路径
    """
    if not dataset:
        print("数据集为空")
        return
    
    # 提取数据
    positions = np.array([sample['current_position'] for sample in dataset])
    velocities = np.array([sample['current_velocity'] for sample in dataset])
    actions = np.array([sample['teacher_action'] for sample in dataset])
    
    # 创建图形
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    
    # 位置分布
    for i, label in enumerate(['X', 'Y', 'Z']):
        axes[0, i].hist(positions[:, i], bins=50, alpha=0.7)
        axes[0, i].set_title(f'Position {label} Distribution')
        axes[0, i].set_xlabel(f'{label} (m)')
        axes[0, i].set_ylabel('Frequency')
        axes[0, i].grid(True)
    
    # 动作分布
    for i, label in enumerate(['Vx', 'Vy', 'Vz']):
        axes[1, i].hist(actions[:, i], bins=50, alpha=0.7)
        axes[1, i].set_title(f'Action {label} Distribution')
        axes[1, i].set_xlabel(f'{label} (m/s)')
        axes[1, i].set_ylabel('Frequency')
        axes[1, i].grid(True)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"统计图已保存: {save_path}")
    
    plt.show()

def compute_dataset_statistics(dataset: List[Dict]) -> Dict:
    """
    计算数据集统计信息
    
    Args:
        dataset: 数据集
        
    Returns:
        statistics: 统计信息字典
    """
    if not dataset:
        return {}
    
    # 提取数据
    positions = np.array([sample['current_position'] for sample in dataset])
    velocities = np.array([sample['current_velocity'] for sample in dataset])
    actions = np.array([sample['teacher_action'] for sample in dataset])
    
    # 计算统计信息
    stats = {
        'num_samples': len(dataset),
        'position_stats': {
            'mean': np.mean(positions, axis=0),
            'std': np.std(positions, axis=0),
            'min': np.min(positions, axis=0),
            'max': np.max(positions, axis=0)
        },
        'velocity_stats': {
            'mean': np.mean(velocities, axis=0),
            'std': np.std(velocities, axis=0),
            'min': np.min(velocities, axis=0),
            'max': np.max(velocities, axis=0)
        },
        'action_stats': {
            'mean': np.mean(actions, axis=0),
            'std': np.std(actions, axis=0),
            'min': np.min(actions, axis=0),
            'max': np.max(actions, axis=0)
        }
    }
    
    return stats
