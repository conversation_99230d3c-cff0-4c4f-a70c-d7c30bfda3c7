"""
AirSim接口模块 - 负责与AirSim仿真环境的通信
"""
import airsim
import numpy as np
import cv2
import time
from typing import Tuple, Dict, Optional
from config import Config

class AirSimInterface:
    def __init__(self):
        """初始化AirSim连接"""
        self.client = airsim.MultirotorClient(ip=Config.AIRSIM_IP, port=Config.AIRSIM_PORT)
        self.client.confirmConnection()
        self.client.enableApiControl(True)
        self.client.armDisarm(True)
        
        # 起飞到默认高度
        self.client.takeoffAsync().join()
        
    def get_depth_image(self) -> np.ndarray:
        """获取深度图像"""
        try:
            # 获取深度图像
            responses = self.client.simGetImages([
                airsim.ImageRequest("front_center", airsim.ImageType.DepthPerspective, True, False)
            ])
            
            if responses:
                response = responses[0]
                # 转换为numpy数组
                img1d = np.frombuffer(response.image_data_float, dtype=np.float32)
                img2d = img1d.reshape(response.height, response.width)
                
                # 处理无效值
                img2d[img2d > Config.POINTCLOUD_MAX_DISTANCE] = Config.POINTCLOUD_MAX_DISTANCE
                img2d[img2d < Config.POINTCLOUD_MIN_DISTANCE] = Config.POINTCLOUD_MIN_DISTANCE
                
                return img2d
            else:
                return np.zeros((Config.DEPTH_HEIGHT, Config.DEPTH_WIDTH))
                
        except Exception as e:
            print(f"获取深度图像失败: {e}")
            return np.zeros((Config.DEPTH_HEIGHT, Config.DEPTH_WIDTH))
    
    def get_rgb_image(self) -> np.ndarray:
        """获取RGB图像"""
        try:
            responses = self.client.simGetImages([
                airsim.ImageRequest("front_center", airsim.ImageType.Scene, False, False)
            ])
            
            if responses:
                response = responses[0]
                img1d = np.frombuffer(response.image_data_uint8, dtype=np.uint8)
                img_rgb = img1d.reshape(response.height, response.width, 3)
                return img_rgb
            else:
                return np.zeros((Config.IMAGE_HEIGHT, Config.IMAGE_WIDTH, 3), dtype=np.uint8)
                
        except Exception as e:
            print(f"获取RGB图像失败: {e}")
            return np.zeros((Config.IMAGE_HEIGHT, Config.IMAGE_WIDTH, 3), dtype=np.uint8)
    
    def get_imu_data(self) -> Dict:
        """获取IMU数据"""
        try:
            imu_data = self.client.getImuData()
            return {
                'angular_velocity': {
                    'x': imu_data.angular_velocity.x_val,
                    'y': imu_data.angular_velocity.y_val,
                    'z': imu_data.angular_velocity.z_val
                },
                'linear_acceleration': {
                    'x': imu_data.linear_acceleration.x_val,
                    'y': imu_data.linear_acceleration.y_val,
                    'z': imu_data.linear_acceleration.z_val
                },
                'orientation': {
                    'w': imu_data.orientation.w_val,
                    'x': imu_data.orientation.x_val,
                    'y': imu_data.orientation.y_val,
                    'z': imu_data.orientation.z_val
                }
            }
        except Exception as e:
            print(f"获取IMU数据失败: {e}")
            return {}
    
    def get_position(self) -> Tuple[float, float, float]:
        """获取无人机位置"""
        try:
            state = self.client.getMultirotorState()
            pos = state.kinematics_estimated.position
            return pos.x_val, pos.y_val, pos.z_val
        except Exception as e:
            print(f"获取位置失败: {e}")
            return 0.0, 0.0, 0.0
    
    def get_velocity(self) -> Tuple[float, float, float]:
        """获取无人机速度"""
        try:
            state = self.client.getMultirotorState()
            vel = state.kinematics_estimated.linear_velocity
            return vel.x_val, vel.y_val, vel.z_val
        except Exception as e:
            print(f"获取速度失败: {e}")
            return 0.0, 0.0, 0.0
    
    def set_velocity(self, vx: float, vy: float, vz: float, duration: float = 0.1):
        """设置无人机速度"""
        try:
            self.client.moveByVelocityAsync(vx, vy, vz, duration)
        except Exception as e:
            print(f"设置速度失败: {e}")
    
    def hover(self):
        """悬停"""
        try:
            self.client.hoverAsync().join()
        except Exception as e:
            print(f"悬停失败: {e}")
    
    def land(self):
        """降落"""
        try:
            self.client.landAsync().join()
        except Exception as e:
            print(f"降落失败: {e}")
    
    def reset(self):
        """重置仿真环境"""
        try:
            self.client.reset()
            self.client.enableApiControl(True)
            self.client.armDisarm(True)
            self.client.takeoffAsync().join()
        except Exception as e:
            print(f"重置失败: {e}")
    
    def disconnect(self):
        """断开连接"""
        try:
            self.client.armDisarm(False)
            self.client.enableApiControl(False)
        except Exception as e:
            print(f"断开连接失败: {e}")
