"""
数据采集模块 - 收集训练数据
"""
import numpy as np
import time
import os
import pickle
from typing import List, Dict, Tuple
from datetime import datetime

from .airsim_interface import AirSimInterface
from pointcloud import DepthToPointCloud
from teacher_model import PotentialFieldTeacher
from config import Config

class DataCollector:
    def __init__(self):
        """初始化数据采集器"""
        self.airsim = AirSimInterface()
        self.pointcloud_converter = DepthToPointCloud()
        self.teacher = PotentialFieldTeacher()
        
        # 数据存储
        self.collected_data = []
        
        # 创建数据目录
        Config.create_directories()
        
    def collect_single_sample(self, goal_position: np.ndarray) -> Dict:
        """
        采集单个样本
        
        Args:
            goal_position: 目标位置 [x, y, z]
            
        Returns:
            sample: 包含所有数据的字典
        """
        try:
            # 获取当前状态
            current_pos = np.array(self.airsim.get_position())
            current_vel = np.array(self.airsim.get_velocity())
            
            # 获取传感器数据
            depth_image = self.airsim.get_depth_image()
            rgb_image = self.airsim.get_rgb_image()
            imu_data = self.airsim.get_imu_data()
            
            # 处理深度图像
            all_points, obstacle_points = self.pointcloud_converter.process_depth_image(depth_image)
            
            # 获取教师动作
            teacher_action, teacher_info = self.teacher.get_teaching_action(
                current_pos, current_vel, goal_position, obstacle_points
            )
            
            # 准备IMU数据向量
            imu_vector = self._prepare_imu_vector(imu_data)
            
            # 构建样本
            sample = {
                'timestamp': time.time(),
                'current_position': current_pos,
                'current_velocity': current_vel,
                'goal_position': goal_position,
                'depth_image': depth_image,
                'rgb_image': rgb_image,
                'imu_data': imu_vector,
                'pointcloud': all_points,
                'obstacle_points': obstacle_points,
                'teacher_action': teacher_action,
                'teacher_info': teacher_info
            }
            
            return sample
            
        except Exception as e:
            print(f"采集样本失败: {e}")
            return None
    
    def _prepare_imu_vector(self, imu_data: Dict) -> np.ndarray:
        """
        准备IMU数据向量
        
        Args:
            imu_data: IMU数据字典
            
        Returns:
            imu_vector: IMU向量 [ax, ay, az, gx, gy, gz, qw, qx, qy, qz]
        """
        if not imu_data:
            return np.zeros(10)
        
        try:
            imu_vector = np.array([
                imu_data['linear_acceleration']['x'],
                imu_data['linear_acceleration']['y'],
                imu_data['linear_acceleration']['z'],
                imu_data['angular_velocity']['x'],
                imu_data['angular_velocity']['y'],
                imu_data['angular_velocity']['z'],
                imu_data['orientation']['w'],
                imu_data['orientation']['x'],
                imu_data['orientation']['y'],
                imu_data['orientation']['z']
            ])
            return imu_vector
        except KeyError:
            return np.zeros(10)
    
    def collect_trajectory(self, goal_position: np.ndarray, 
                          max_time: float = 60.0,
                          control_frequency: float = None) -> List[Dict]:
        """
        采集一条轨迹的数据
        
        Args:
            goal_position: 目标位置
            max_time: 最大采集时间(秒)
            control_frequency: 控制频率(Hz)
            
        Returns:
            trajectory_data: 轨迹数据列表
        """
        if control_frequency is None:
            control_frequency = Config.CONTROL_FREQUENCY
            
        dt = 1.0 / control_frequency
        trajectory_data = []
        
        start_time = time.time()
        
        print(f"开始采集轨迹数据，目标位置: {goal_position}")
        
        while time.time() - start_time < max_time:
            # 采集样本
            sample = self.collect_single_sample(goal_position)
            
            if sample is not None:
                trajectory_data.append(sample)
                
                # 执行教师动作
                teacher_action = sample['teacher_action']
                self.airsim.set_velocity(teacher_action[0], teacher_action[1], teacher_action[2], dt)
                
                # 检查是否到达目标
                if sample['teacher_info']['goal_reached']:
                    print("到达目标位置")
                    break
                
                # 检查碰撞风险
                if sample['teacher_info']['collision_risk']:
                    print("检测到碰撞风险，停止采集")
                    break
            
            time.sleep(dt)
        
        print(f"轨迹采集完成，共采集 {len(trajectory_data)} 个样本")
        return trajectory_data
    
    def collect_dataset(self, num_trajectories: int = 10,
                       goal_positions: List[np.ndarray] = None) -> List[Dict]:
        """
        采集完整数据集
        
        Args:
            num_trajectories: 轨迹数量
            goal_positions: 目标位置列表，如果为None则随机生成
            
        Returns:
            dataset: 完整数据集
        """
        if goal_positions is None:
            goal_positions = self._generate_random_goals(num_trajectories)
        
        dataset = []
        
        for i, goal_pos in enumerate(goal_positions):
            print(f"采集第 {i+1}/{len(goal_positions)} 条轨迹")
            
            # 重置环境
            self.airsim.reset()
            time.sleep(2.0)  # 等待环境稳定
            
            # 采集轨迹
            trajectory = self.collect_trajectory(goal_pos)
            dataset.extend(trajectory)
            
            print(f"轨迹 {i+1} 完成，当前总样本数: {len(dataset)}")
        
        self.collected_data = dataset
        return dataset
    
    def _generate_random_goals(self, num_goals: int) -> List[np.ndarray]:
        """
        生成随机目标位置
        
        Args:
            num_goals: 目标数量
            
        Returns:
            goals: 目标位置列表
        """
        goals = []
        
        for _ in range(num_goals):
            # 在合理范围内生成随机目标
            x = np.random.uniform(-20, 20)
            y = np.random.uniform(-20, 20)
            z = np.random.uniform(-10, -2)  # 负值表示高度
            
            goals.append(np.array([x, y, z]))
        
        return goals
    
    def save_dataset(self, filename: str = None) -> str:
        """
        保存数据集
        
        Args:
            filename: 文件名，如果为None则自动生成
            
        Returns:
            filepath: 保存的文件路径
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"dataset_{timestamp}.pkl"
        
        filepath = os.path.join(Config.DATA_DIR, filename)
        
        with open(filepath, 'wb') as f:
            pickle.dump(self.collected_data, f)
        
        print(f"数据集已保存到: {filepath}")
        print(f"总样本数: {len(self.collected_data)}")
        
        return filepath
    
    def load_dataset(self, filepath: str) -> List[Dict]:
        """
        加载数据集
        
        Args:
            filepath: 文件路径
            
        Returns:
            dataset: 数据集
        """
        with open(filepath, 'rb') as f:
            dataset = pickle.load(f)
        
        self.collected_data = dataset
        print(f"数据集已加载，样本数: {len(dataset)}")
        
        return dataset
    
    def get_dataset_statistics(self) -> Dict:
        """
        获取数据集统计信息
        
        Returns:
            stats: 统计信息字典
        """
        if not self.collected_data:
            return {}
        
        # 计算统计信息
        positions = np.array([sample['current_position'] for sample in self.collected_data])
        velocities = np.array([sample['current_velocity'] for sample in self.collected_data])
        actions = np.array([sample['teacher_action'] for sample in self.collected_data])
        
        stats = {
            'num_samples': len(self.collected_data),
            'position_stats': {
                'mean': np.mean(positions, axis=0),
                'std': np.std(positions, axis=0),
                'min': np.min(positions, axis=0),
                'max': np.max(positions, axis=0)
            },
            'velocity_stats': {
                'mean': np.mean(velocities, axis=0),
                'std': np.std(velocities, axis=0),
                'min': np.min(velocities, axis=0),
                'max': np.max(velocities, axis=0)
            },
            'action_stats': {
                'mean': np.mean(actions, axis=0),
                'std': np.std(actions, axis=0),
                'min': np.min(actions, axis=0),
                'max': np.max(actions, axis=0)
            }
        }
        
        return stats
    
    def cleanup(self):
        """清理资源"""
        self.airsim.disconnect()
