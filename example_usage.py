#!/usr/bin/env python3
"""
使用示例脚本 - 演示系统的基本使用方法
"""
import numpy as np
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def example_data_collection():
    """示例：数据采集"""
    print("=== 数据采集示例 ===")
    print("注意：此示例需要AirSim正在运行")
    
    try:
        from data_collection import DataCollector
        
        # 创建数据采集器
        collector = DataCollector()
        
        # 设置目标位置
        goal_positions = [
            np.array([10.0, 0.0, -5.0]),
            np.array([0.0, 10.0, -5.0]),
            np.array([-10.0, 0.0, -5.0])
        ]
        
        print(f"将采集 {len(goal_positions)} 条轨迹")
        
        # 采集数据（模拟模式，不实际连接AirSim）
        print("模拟数据采集...")
        
        # 创建模拟数据
        dataset = []
        for i in range(50):  # 模拟50个样本
            sample = {
                'timestamp': i * 0.1,
                'current_position': np.random.uniform(-5, 5, 3),
                'current_velocity': np.random.uniform(-1, 1, 3),
                'goal_position': goal_positions[i % len(goal_positions)],
                'depth_image': np.random.uniform(0.5, 10.0, (144, 256)),
                'rgb_image': np.random.randint(0, 255, (144, 256, 3), dtype=np.uint8),
                'imu_data': np.random.randn(10),
                'pointcloud': np.random.uniform(-5, 5, (100, 3)),
                'obstacle_points': np.random.uniform(-3, 3, (20, 3)),
                'teacher_action': np.random.uniform(-1, 1, 3),
                'teacher_info': {
                    'goal_reached': False,
                    'collision_risk': False,
                    'distance_to_goal': np.random.uniform(1, 10),
                    'num_obstacles': 20
                }
            }
            dataset.append(sample)
        
        # 保存数据
        import pickle
        from config import Config
        Config.create_directories()
        
        filepath = os.path.join(Config.DATA_DIR, 'example_dataset.pkl')
        with open(filepath, 'wb') as f:
            pickle.dump(dataset, f)
        
        print(f"模拟数据已保存到: {filepath}")
        print(f"数据集大小: {len(dataset)} 样本")
        
        return filepath
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保所有依赖已安装")
        return None
    except Exception as e:
        print(f"数据采集示例失败: {e}")
        return None

def example_training(dataset_path):
    """示例：模型训练"""
    print("\n=== 模型训练示例 ===")
    
    if not dataset_path or not os.path.exists(dataset_path):
        print("错误: 数据集文件不存在")
        return None
    
    try:
        # 加载数据集
        import pickle
        with open(dataset_path, 'rb') as f:
            dataset = pickle.load(f)
        
        print(f"数据集已加载，样本数: {len(dataset)}")
        
        # 创建训练器
        from training import create_trainer
        trainer = create_trainer(use_imu=True)
        
        print(f"模型参数数量: {sum(p.numel() for p in trainer.model.parameters() if p.requires_grad)}")
        
        # 快速训练（少量epoch用于演示）
        print("开始快速训练演示（5个epoch）...")
        
        training_history = trainer.train(
            dataset=dataset,
            num_epochs=5,  # 少量epoch用于演示
            train_ratio=0.8,
            save_best=True
        )
        
        print("训练完成!")
        print(f"最终训练损失: {training_history['train_losses'][-1]:.6f}")
        print(f"最终验证损失: {training_history['val_losses'][-1]:.6f}")
        
        # 返回最佳模型路径
        from config import Config
        return os.path.join(Config.MODEL_DIR, 'best_model.pth')
        
    except Exception as e:
        print(f"训练示例失败: {e}")
        return None

def example_inference(model_path):
    """示例：模型推理"""
    print("\n=== 模型推理示例 ===")
    
    if not model_path or not os.path.exists(model_path):
        print("错误: 模型文件不存在")
        return
    
    try:
        # 加载模型
        from student_model import load_model
        model = load_model(model_path, use_imu=True)
        
        print("模型已加载")
        
        # 创建测试数据
        depth_image = np.random.uniform(0.5, 10.0, (144, 256))
        imu_data = np.random.randn(10)
        
        # 进行推理
        action = model.predict(depth_image, imu_data)
        
        print(f"输入深度图像尺寸: {depth_image.shape}")
        print(f"输入IMU数据尺寸: {imu_data.shape}")
        print(f"预测动作: {action}")
        print(f"动作范围: [{action.min():.3f}, {action.max():.3f}]")
        
    except Exception as e:
        print(f"推理示例失败: {e}")

def example_teacher_model():
    """示例：教师模型使用"""
    print("\n=== 教师模型示例 ===")
    
    try:
        from teacher_model import PotentialFieldTeacher
        
        # 创建教师模型
        teacher = PotentialFieldTeacher()
        
        # 设置场景
        current_pos = np.array([0.0, 0.0, -5.0])
        current_vel = np.array([0.0, 0.0, 0.0])
        goal_pos = np.array([10.0, 0.0, -5.0])
        
        # 创建障碍物
        obstacle_points = np.array([
            [5.0, 0.0, -5.0],   # 正前方障碍物
            [3.0, 1.0, -5.0],   # 右前方障碍物
            [4.0, -1.0, -5.0]   # 左前方障碍物
        ])
        
        # 计算教师动作
        action, info = teacher.get_teaching_action(
            current_pos, current_vel, goal_pos, obstacle_points
        )
        
        print(f"当前位置: {current_pos}")
        print(f"目标位置: {goal_pos}")
        print(f"障碍物数量: {len(obstacle_points)}")
        print(f"教师动作: {action}")
        print(f"到目标距离: {info['distance_to_goal']:.3f}m")
        print(f"碰撞风险: {info['collision_risk']}")
        print(f"目标到达: {info['goal_reached']}")
        
    except Exception as e:
        print(f"教师模型示例失败: {e}")

def example_pointcloud_processing():
    """示例：点云处理"""
    print("\n=== 点云处理示例 ===")
    
    try:
        from pointcloud import DepthToPointCloud
        
        # 创建点云转换器
        converter = DepthToPointCloud()
        
        # 创建模拟深度图像
        depth_image = np.random.uniform(0.5, 10.0, (144, 256))
        
        # 添加一些"障碍物"（近距离区域）
        depth_image[60:80, 120:140] = 2.0  # 中心障碍物
        depth_image[100:120, 200:220] = 1.5  # 右侧障碍物
        
        print(f"深度图像尺寸: {depth_image.shape}")
        print(f"深度范围: [{depth_image.min():.3f}, {depth_image.max():.3f}]")
        
        # 处理深度图像
        all_points, obstacle_points = converter.process_depth_image(depth_image)
        
        print(f"总点云数量: {len(all_points)}")
        print(f"障碍物点数量: {len(obstacle_points)}")
        
        if len(obstacle_points) > 0:
            print(f"最近障碍物距离: {np.min(np.linalg.norm(obstacle_points, axis=1)):.3f}m")
        
    except Exception as e:
        print(f"点云处理示例失败: {e}")

def example_visualization():
    """示例：可视化功能"""
    print("\n=== 可视化示例 ===")
    
    try:
        from visualization import create_visualizer, DataLogger
        
        # 创建可视化器和日志记录器
        visualizer = create_visualizer()
        logger = DataLogger()
        
        print("可视化器已创建")
        
        # 模拟一些数据更新
        for i in range(5):
            # 模拟飞行数据
            data = {
                'position': np.array([i, i*0.5, -5.0]),
                'velocity': np.array([1.0, 0.5, 0.0]),
                'action': np.array([0.8, 0.3, 0.0]),
                'rgb_image': np.random.randint(0, 255, (144, 256, 3), dtype=np.uint8),
                'depth_image': np.random.uniform(0.5, 10.0, (144, 256)),
                'pointcloud': np.random.uniform(-5, 5, (100, 3)),
                'status': {
                    'mode': 'teacher',
                    'distance_to_goal': 10.0 - i,
                    'num_obstacles': 5,
                    'safety_intervention': False
                }
            }
            
            visualizer.update_data(data)
            logger.log(data)
        
        print("数据更新完成")
        print("注意: 实际可视化需要调用 visualizer.start_visualization()")
        
    except Exception as e:
        print(f"可视化示例失败: {e}")

def main():
    """主函数 - 运行所有示例"""
    print("深度图像模仿学习无人机避障系统 - 使用示例")
    print("=" * 60)
    
    # 1. 教师模型示例
    example_teacher_model()
    
    # 2. 点云处理示例
    example_pointcloud_processing()
    
    # 3. 数据采集示例
    dataset_path = example_data_collection()
    
    # 4. 模型训练示例
    if dataset_path:
        model_path = example_training(dataset_path)
        
        # 5. 模型推理示例
        if model_path:
            example_inference(model_path)
    
    # 6. 可视化示例
    example_visualization()
    
    print("\n" + "=" * 60)
    print("所有示例运行完成!")
    print("\n使用说明:")
    print("1. 确保AirSim正在运行后，使用 'python main.py collect' 采集真实数据")
    print("2. 使用 'python main.py train --dataset <数据集路径>' 训练模型")
    print("3. 使用 'python main.py fly --mode student --model <模型路径> --visualize' 进行飞行")

if __name__ == "__main__":
    main()
