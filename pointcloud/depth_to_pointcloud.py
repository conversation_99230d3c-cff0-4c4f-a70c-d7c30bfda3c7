"""
深度图像转点云模块
"""
import numpy as np
import open3d as o3d
from typing import Tuple
from config import Config

class DepthToPointCloud:
    def __init__(self, fov_degrees: float = 90.0):
        """
        初始化深度图像转点云转换器
        
        Args:
            fov_degrees: 相机视场角(度)
        """
        self.fov_degrees = fov_degrees
        self.fov_radians = np.radians(fov_degrees)
        
        # 计算相机内参
        self.width = Config.DEPTH_WIDTH
        self.height = Config.DEPTH_HEIGHT
        
        # 假设主点在图像中心
        self.cx = self.width / 2.0
        self.cy = self.height / 2.0
        
        # 根据FOV计算焦距
        self.fx = self.width / (2.0 * np.tan(self.fov_radians / 2.0))
        self.fy = self.fx  # 假设fx = fy
        
        # 创建像素坐标网格
        self.u, self.v = np.meshgrid(np.arange(self.width), np.arange(self.height))
        
    def depth_to_pointcloud(self, depth_image: np.ndarray) -> np.ndarray:
        """
        将深度图像转换为点云
        
        Args:
            depth_image: 深度图像 (H, W)
            
        Returns:
            points: 点云数组 (N, 3) - [x, y, z]
        """
        # 确保深度图像尺寸正确
        if depth_image.shape != (self.height, self.width):
            depth_image = np.resize(depth_image, (self.height, self.width))
        
        # 过滤有效深度值
        valid_mask = (depth_image > Config.POINTCLOUD_MIN_DISTANCE) & \
                    (depth_image < Config.POINTCLOUD_MAX_DISTANCE)
        
        # 获取有效像素坐标和深度值
        u_valid = self.u[valid_mask]
        v_valid = self.v[valid_mask]
        z_valid = depth_image[valid_mask]
        
        # 转换为3D坐标 (相机坐标系)
        x = (u_valid - self.cx) * z_valid / self.fx
        y = (v_valid - self.cy) * z_valid / self.fy
        z = z_valid
        
        # 组合为点云
        points = np.column_stack((x, y, z))
        
        return points
    
    def pointcloud_to_o3d(self, points: np.ndarray) -> o3d.geometry.PointCloud:
        """
        将numpy点云转换为Open3D点云对象
        
        Args:
            points: 点云数组 (N, 3)
            
        Returns:
            o3d_pointcloud: Open3D点云对象
        """
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points)
        return pcd
    
    def downsample_pointcloud(self, points: np.ndarray, voxel_size: float = None) -> np.ndarray:
        """
        下采样点云
        
        Args:
            points: 原始点云 (N, 3)
            voxel_size: 体素大小，默认使用配置值
            
        Returns:
            downsampled_points: 下采样后的点云
        """
        if voxel_size is None:
            voxel_size = Config.POINTCLOUD_DOWNSAMPLE_VOXEL
            
        if len(points) == 0:
            return points
            
        # 转换为Open3D点云
        pcd = self.pointcloud_to_o3d(points)
        
        # 下采样
        pcd_downsampled = pcd.voxel_down_sample(voxel_size)
        
        # 转换回numpy数组
        return np.asarray(pcd_downsampled.points)
    
    def filter_pointcloud_by_distance(self, points: np.ndarray, 
                                    min_distance: float = None, 
                                    max_distance: float = None) -> np.ndarray:
        """
        根据距离过滤点云
        
        Args:
            points: 点云 (N, 3)
            min_distance: 最小距离
            max_distance: 最大距离
            
        Returns:
            filtered_points: 过滤后的点云
        """
        if min_distance is None:
            min_distance = Config.POINTCLOUD_MIN_DISTANCE
        if max_distance is None:
            max_distance = Config.POINTCLOUD_MAX_DISTANCE
            
        if len(points) == 0:
            return points
            
        # 计算每个点到原点的距离
        distances = np.linalg.norm(points, axis=1)
        
        # 过滤
        valid_mask = (distances >= min_distance) & (distances <= max_distance)
        
        return points[valid_mask]
    
    def get_obstacle_points(self, points: np.ndarray, threshold_distance: float = None) -> np.ndarray:
        """
        获取障碍物点云（距离小于阈值的点）
        
        Args:
            points: 点云 (N, 3)
            threshold_distance: 障碍物距离阈值
            
        Returns:
            obstacle_points: 障碍物点云
        """
        if threshold_distance is None:
            threshold_distance = Config.OBSTACLE_THRESHOLD
            
        if len(points) == 0:
            return points
            
        # 计算距离
        distances = np.linalg.norm(points, axis=1)
        
        # 获取障碍物点
        obstacle_mask = distances < threshold_distance
        
        return points[obstacle_mask]
    
    def process_depth_image(self, depth_image: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        完整处理深度图像：转换为点云并进行预处理
        
        Args:
            depth_image: 深度图像
            
        Returns:
            all_points: 所有点云
            obstacle_points: 障碍物点云
        """
        # 转换为点云
        points = self.depth_to_pointcloud(depth_image)
        
        # 距离过滤
        points = self.filter_pointcloud_by_distance(points)
        
        # 下采样
        points = self.downsample_pointcloud(points)
        
        # 获取障碍物点
        obstacle_points = self.get_obstacle_points(points)
        
        return points, obstacle_points
