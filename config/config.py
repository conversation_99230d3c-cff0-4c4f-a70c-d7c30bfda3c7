"""
配置文件 - 包含所有系统参数
"""
import os

class Config:
    # AirSim连接配置
    AIRSIM_IP = "127.0.0.1"
    AIRSIM_PORT = 41451
    
    # 图像配置
    IMAGE_WIDTH = 256
    IMAGE_HEIGHT = 144
    DEPTH_WIDTH = 256
    DEPTH_HEIGHT = 144
    
    # 点云配置
    POINTCLOUD_MAX_DISTANCE = 10.0  # 最大检测距离(米)
    POINTCLOUD_MIN_DISTANCE = 0.5   # 最小检测距离(米)
    POINTCLOUD_DOWNSAMPLE_VOXEL = 0.1  # 下采样体素大小
    
    # 教师模型(人工势场)配置
    ATTRACTIVE_GAIN = 1.0      # 引力增益
    REPULSIVE_GAIN = 2.0       # 斥力增益
    OBSTACLE_THRESHOLD = 3.0   # 障碍物影响阈值(米)
    GOAL_THRESHOLD = 0.5       # 目标到达阈值(米)
    MAX_VELOCITY = 2.0         # 最大速度(m/s)
    
    # 学生模型(CNN)配置
    CNN_INPUT_CHANNELS = 1     # 深度图像通道数
    CNN_HIDDEN_DIM = 512       # 隐藏层维度
    FC_HIDDEN_DIM = 256        # 全连接层维度
    OUTPUT_DIM = 3             # 输出维度(vx, vy, vz)
    
    # 训练配置
    BATCH_SIZE = 32
    LEARNING_RATE = 0.001
    NUM_EPOCHS = 100
    TRAIN_TEST_SPLIT = 0.8
    
    # 数据采集配置
    DATA_COLLECTION_FREQUENCY = 10  # Hz
    MAX_COLLECTION_TIME = 300       # 秒
    
    # 文件路径
    PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    DATA_DIR = os.path.join(PROJECT_ROOT, "data")
    MODEL_DIR = os.path.join(PROJECT_ROOT, "models")
    LOG_DIR = os.path.join(PROJECT_ROOT, "logs")
    
    # 可视化配置
    VISUALIZATION_UPDATE_RATE = 30  # FPS
    PLOT_HISTORY_LENGTH = 100       # 历史数据显示长度
    
    # 控制配置
    CONTROL_FREQUENCY = 20          # Hz
    SAFETY_DISTANCE = 1.0           # 安全距离(米)
    
    @classmethod
    def create_directories(cls):
        """创建必要的目录"""
        for dir_path in [cls.DATA_DIR, cls.MODEL_DIR, cls.LOG_DIR]:
            os.makedirs(dir_path, exist_ok=True)
