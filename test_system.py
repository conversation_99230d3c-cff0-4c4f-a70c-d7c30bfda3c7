#!/usr/bin/env python3
"""
系统测试脚本 - 验证各模块功能
"""
import numpy as np
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_config():
    """测试配置模块"""
    print("测试配置模块...")
    try:
        from config import Config
        Config.create_directories()
        print("✓ 配置模块正常")
        return True
    except Exception as e:
        print(f"✗ 配置模块错误: {e}")
        return False

def test_pointcloud():
    """测试点云处理模块"""
    print("测试点云处理模块...")
    try:
        from pointcloud import DepthToPointCloud
        
        # 创建测试深度图像
        depth_image = np.random.uniform(0.5, 10.0, (144, 256))
        
        # 初始化转换器
        converter = DepthToPointCloud()
        
        # 转换为点云
        points = converter.depth_to_pointcloud(depth_image)
        
        # 处理点云
        all_points, obstacle_points = converter.process_depth_image(depth_image)
        
        print(f"✓ 点云处理正常，生成 {len(points)} 个点")
        return True
    except Exception as e:
        print(f"✗ 点云处理错误: {e}")
        return False

def test_teacher_model():
    """测试教师模型"""
    print("测试教师模型...")
    try:
        from teacher_model import PotentialFieldTeacher
        
        teacher = PotentialFieldTeacher()
        
        # 测试数据
        current_pos = np.array([0.0, 0.0, -5.0])
        goal_pos = np.array([10.0, 0.0, -5.0])
        obstacle_points = np.array([[5.0, 0.0, -5.0], [3.0, 1.0, -5.0]])
        
        # 计算动作
        action, info = teacher.get_teaching_action(
            current_pos, np.zeros(3), goal_pos, obstacle_points
        )
        
        print(f"✓ 教师模型正常，动作: {action}")
        return True
    except Exception as e:
        print(f"✗ 教师模型错误: {e}")
        return False

def test_student_model():
    """测试学生模型"""
    print("测试学生模型...")
    try:
        from student_model import create_model, count_parameters
        
        # 创建模型
        model = create_model(use_imu=True)
        
        # 测试前向传播
        depth_image = np.random.randn(1, 1, 144, 256).astype(np.float32)
        imu_data = np.random.randn(1, 10).astype(np.float32)
        
        import torch
        depth_tensor = torch.FloatTensor(depth_image)
        imu_tensor = torch.FloatTensor(imu_data)
        
        with torch.no_grad():
            output = model(depth_tensor, imu_tensor)
        
        param_count = count_parameters(model)
        
        print(f"✓ 学生模型正常，参数数量: {param_count}")
        return True
    except Exception as e:
        print(f"✗ 学生模型错误: {e}")
        return False

def test_dataset():
    """测试数据集处理"""
    print("测试数据集处理...")
    try:
        from training import ObstacleAvoidanceDataset
        
        # 创建模拟数据
        sample_data = []
        for i in range(10):
            sample = {
                'depth_image': np.random.uniform(0.5, 10.0, (144, 256)),
                'imu_data': np.random.randn(10),
                'teacher_action': np.random.randn(3)
            }
            sample_data.append(sample)
        
        # 创建数据集
        dataset = ObstacleAvoidanceDataset(sample_data, use_imu=True, normalize=True)
        
        # 测试数据加载
        depth, imu, action = dataset[0]
        
        print(f"✓ 数据集处理正常，数据集大小: {len(dataset)}")
        return True
    except Exception as e:
        print(f"✗ 数据集处理错误: {e}")
        return False

def test_utils():
    """测试工具函数"""
    print("测试工具函数...")
    try:
        from utils import normalize_depth_image, split_dataset
        
        # 测试深度图像归一化
        depth_image = np.random.uniform(0.5, 10.0, (144, 256))
        normalized = normalize_depth_image(depth_image)
        
        # 测试数据集划分
        dataset = [{'data': i} for i in range(100)]
        train_set, val_set, test_set = split_dataset(dataset)
        
        print(f"✓ 工具函数正常，数据集划分: {len(train_set)}/{len(val_set)}/{len(test_set)}")
        return True
    except Exception as e:
        print(f"✗ 工具函数错误: {e}")
        return False

def test_visualization():
    """测试可视化模块"""
    print("测试可视化模块...")
    try:
        from visualization import create_visualizer, DataLogger
        
        # 创建可视化器
        visualizer = create_visualizer()
        
        # 创建数据记录器
        logger = DataLogger()
        
        # 测试数据更新
        test_data = {
            'position': np.array([1.0, 2.0, -3.0]),
            'velocity': np.array([0.5, 0.0, 0.1]),
            'action': np.array([0.3, 0.0, 0.0])
        }
        
        visualizer.update_data(test_data)
        logger.log(test_data)
        
        print("✓ 可视化模块正常")
        return True
    except Exception as e:
        print(f"✗ 可视化模块错误: {e}")
        return False

def test_imports():
    """测试所有模块导入"""
    print("测试模块导入...")
    
    modules_to_test = [
        'config',
        'pointcloud', 
        'teacher_model',
        'student_model',
        'training',
        'control',
        'visualization',
        'utils'
    ]
    
    failed_modules = []
    
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"✓ {module_name} 导入成功")
        except Exception as e:
            print(f"✗ {module_name} 导入失败: {e}")
            failed_modules.append(module_name)
    
    return len(failed_modules) == 0

def run_all_tests():
    """运行所有测试"""
    print("=" * 50)
    print("开始系统测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("配置模块", test_config),
        ("点云处理", test_pointcloud),
        ("教师模型", test_teacher_model),
        ("学生模型", test_student_model),
        ("数据集处理", test_dataset),
        ("工具函数", test_utils),
        ("可视化模块", test_visualization)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试完成: {passed}/{total} 通过")
    print("=" * 50)
    
    if passed == total:
        print("🎉 所有测试通过！系统准备就绪。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关模块。")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
