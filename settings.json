{"SeeDocsAt": "https://github.com/Microsoft/AirSim/blob/master/docs/settings.md", "SettingsVersion": 1.2, "SimMode": "Multirotor", "ViewMode": "SpringArmChase", "ClockSpeed": 10.0, "Vehicles": {"Drone1": {"VehicleType": "SimpleFlight", "DefaultVehicleState": "Armed", "X": 0, "Y": 0, "Z": 0, "Yaw": 0, "EnableCollisionPassthrogh": false, "EnableCollisions": true, "AllowAPIAlways": true, "Cameras": {"front_center": {"CaptureSettings": [{"ImageType": 0, "Width": 256, "Height": 144, "FOV_Degrees": 90}, {"ImageType": 2, "Width": 256, "Height": 144, "FOV_Degrees": 90}, {"ImageType": 3, "Width": 128, "Height": 72, "FOV_Degrees": 90}], "X": 0.3, "Y": 0.0, "Z": 0.0, "Pitch": 0.0, "Roll": 0.0, "Yaw": 0.0}}, "Sensors": {"Imu": {"SensorType": 2, "Enabled": true}, "Distance": {"SensorType": 5, "Enabled": true, "DrawDebugPoints": false}}}}, "OriginGeopoint": {"Latitude": 47.641468, "Longitude": -122.140165, "Altitude": 122}, "SubWindows": [{"WindowID": 0, "ImageType": 0, "CameraName": "front_center", "Visible": true}, {"WindowID": 1, "ImageType": 3, "CameraName": "front_center", "Visible": false}, {"WindowID": 2, "ImageType": 3, "CameraName": "front_center", "Visible": true}], "Recording": {"RecordOnMove": false, "RecordInterval": 0.05}, "UnrealEngine": {"PixelFormatOverrides": [{"cam_name": "0", "img_type": "Scene", "pixel_format": "BGR8"}]}}