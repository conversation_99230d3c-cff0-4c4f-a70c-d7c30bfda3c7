# 深度图像模仿学习无人机避障系统

基于AirSim仿真环境的深度图像数据驱动的无人机自动避障系统，采用模仿学习方法，教师模型使用人工势场算法，学生模型使用CNN+FC网络架构。

## 项目特性

- **深度图像处理**: 将深度图像转换为点云进行障碍物检测
- **模仿学习**: 学生模型学习教师模型(人工势场)的避障策略
- **多模态输入**: 支持深度图像和IMU数据融合
- **实时可视化**: 提供实时飞行状态和数据可视化
- **安全控制**: 内置安全检查和紧急干预机制
- **多种控制模式**: 支持教师、学生、混合和手动控制模式

## 系统架构

```
deep_il_3d_1/
├── config/                 # 配置管理
│   ├── __init__.py
│   └── config.py
├── data_collection/         # 数据采集模块
│   ├── __init__.py
│   ├── airsim_interface.py  # AirSim接口
│   └── data_collector.py    # 数据采集器
├── pointcloud/             # 点云处理模块
│   ├── __init__.py
│   └── depth_to_pointcloud.py
├── teacher_model/          # 教师模型(人工势场)
│   ├── __init__.py
│   └── potential_field.py
├── student_model/          # 学生模型(CNN+FC)
│   ├── __init__.py
│   └── cnn_model.py
├── training/               # 训练模块
│   ├── __init__.py
│   ├── dataset.py          # 数据集处理
│   └── trainer.py          # 模型训练
├── control/                # 控制模块
│   ├── __init__.py
│   └── controller.py       # 无人机控制器
├── visualization/          # 可视化模块
│   ├── __init__.py
│   └── visualizer.py       # 实时可视化
├── utils/                  # 工具函数
│   ├── __init__.py
│   └── data_utils.py
├── settings.json           # AirSim配置文件
├── requirements.txt        # 依赖包
├── main.py                # 主程序入口
└── README.md              # 项目说明
```

## 安装和配置

### 1. 环境要求

- Python 3.7+
- AirSim 1.8.1+
- CUDA (可选，用于GPU训练)

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. AirSim配置

确保AirSim已正确安装并运行。项目包含的`settings.json`文件已配置好相机和传感器参数。

将`settings.json`复制到AirSim配置目录：
- Windows: `%USERPROFILE%/Documents/AirSim/`
- Linux: `~/Documents/AirSim/`

## 使用方法

### 1. 数据采集

首先启动AirSim仿真环境，然后运行数据采集：

```bash
# 采集10条轨迹的训练数据
python main.py collect --num-trajectories 10 --visualize

# 指定输出文件名
python main.py collect --num-trajectories 20 --output my_dataset.pkl
```

### 2. 模型训练

使用采集的数据训练学生模型：

```bash
# 基本训练
python main.py train --dataset data/dataset_20231201_120000.pkl

# 指定训练轮数
python main.py train --dataset data/dataset_20231201_120000.pkl --epochs 200

# 不使用IMU数据训练
python main.py train --dataset data/dataset_20231201_120000.pkl --no-imu
```

### 3. 飞行控制

训练完成后，可以使用不同模式进行飞行：

```bash
# 教师模式飞行(人工势场)
python main.py fly --mode teacher --goal 10 0 -5 --visualize

# 学生模式飞行(CNN模型)
python main.py fly --mode student --model models/best_model.pth --goal 10 0 -5 --visualize

# 混合模式飞行(安全时用学生，危险时用教师)
python main.py fly --mode hybrid --model models/best_model.pth --goal 10 0 -5 --visualize

# 指定飞行时长
python main.py fly --mode student --model models/best_model.pth --duration 120 --visualize
```

## 配置参数

主要配置参数在`config/config.py`中：

```python
# 图像配置
IMAGE_WIDTH = 256
IMAGE_HEIGHT = 144
DEPTH_WIDTH = 256
DEPTH_HEIGHT = 144

# 点云配置
POINTCLOUD_MAX_DISTANCE = 10.0  # 最大检测距离(米)
POINTCLOUD_MIN_DISTANCE = 0.5   # 最小检测距离(米)

# 教师模型配置
ATTRACTIVE_GAIN = 1.0      # 引力增益
REPULSIVE_GAIN = 2.0       # 斥力增益
OBSTACLE_THRESHOLD = 3.0   # 障碍物影响阈值(米)

# 训练配置
BATCH_SIZE = 32
LEARNING_RATE = 0.001
NUM_EPOCHS = 100

# 控制配置
CONTROL_FREQUENCY = 20     # 控制频率(Hz)
SAFETY_DISTANCE = 1.0      # 安全距离(米)
```

## 网络架构

### 学生模型 (CNN + FC)

```
输入: 深度图像 (1, 144, 256) + IMU数据 (10,)
├── 深度CNN分支:
│   ├── Conv2d(1→32, 8x8, stride=4) + BN + ReLU
│   ├── Conv2d(32→64, 4x4, stride=2) + BN + ReLU  
│   ├── Conv2d(64→128, 3x3, stride=1) + BN + ReLU
│   └── Conv2d(128→256, 3x3, stride=1) + BN + ReLU
├── IMU编码器:
│   ├── Linear(10→64) + BN + ReLU
│   └── Linear(64→64) + ReLU
├── 特征融合:
│   ├── Concat(CNN特征 + IMU特征)
│   ├── Linear(融合维度→512) + BN + ReLU
│   └── Linear(512→256) + BN + ReLU
└── 输出层: Linear(256→3) + Tanh
输出: 速度命令 (vx, vy, vz)
```

## 可视化功能

实时可视化包含以下内容：

- **RGB图像**: 前置摄像头视图
- **深度图像**: 深度传感器数据
- **3D轨迹**: 无人机飞行轨迹
- **历史曲线**: 位置、速度、动作历史
- **点云显示**: 实时障碍物点云
- **状态信息**: 飞行状态和统计信息

## 安全特性

- **碰撞检测**: 基于点云的实时碰撞风险评估
- **速度限制**: 自动限制最大飞行速度
- **安全干预**: 危险情况下自动切换到安全控制
- **紧急停止**: 支持紧急悬停和降落

## 性能优化

- **数据增强**: 深度图像噪声和亮度增强
- **批归一化**: 加速训练收敛
- **梯度裁剪**: 防止梯度爆炸
- **学习率调度**: 自适应学习率调整
- **早停机制**: 防止过拟合

## 故障排除

### 常见问题

1. **AirSim连接失败**
   - 确保AirSim正在运行
   - 检查IP和端口配置
   - 重启AirSim和脚本

2. **深度图像数据异常**
   - 检查相机配置
   - 确认深度传感器已启用
   - 调整深度范围参数

3. **训练收敛慢**
   - 增加数据集大小
   - 调整学习率
   - 检查数据质量

4. **飞行不稳定**
   - 降低控制频率
   - 调整安全参数
   - 检查模型预测质量

## 扩展功能

- 支持多无人机协同避障
- 添加语义分割增强感知
- 集成强化学习优化
- 支持动态障碍物避障
- 添加路径规划模块

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题请提交Issue或联系项目维护者。
