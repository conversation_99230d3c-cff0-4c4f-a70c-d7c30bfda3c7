"""
控制模块 - 无人机控制接口
"""
import numpy as np
import time
from typing import Tuple, Optional
from enum import Enum

from data_collection import AirSimInterface
from pointcloud import DepthToPointCloud
from teacher_model import PotentialFieldTeacher
from student_model import ObstacleAvoidanceNet, load_model
from config import Config

class ControlMode(Enum):
    """控制模式枚举"""
    MANUAL = "manual"
    TEACHER = "teacher"
    STUDENT = "student"
    HYBRID = "hybrid"

class DroneController:
    """无人机控制器"""
    
    def __init__(self, model_path: Optional[str] = None):
        """
        初始化控制器
        
        Args:
            model_path: 学生模型路径，如果为None则只能使用教师模式
        """
        # 初始化接口
        self.airsim = AirSimInterface()
        self.pointcloud_converter = DepthToPointCloud()
        self.teacher = PotentialFieldTeacher()
        
        # 加载学生模型
        self.student_model = None
        if model_path:
            try:
                self.student_model = load_model(model_path, use_imu=True)
                self.student_model.eval()
                print(f"学生模型已加载: {model_path}")
            except Exception as e:
                print(f"加载学生模型失败: {e}")
        
        # 控制状态
        self.control_mode = ControlMode.TEACHER
        self.current_goal = np.array([0.0, 0.0, -5.0])  # 默认目标
        self.is_active = False
        
        # 安全参数
        self.max_velocity = Config.MAX_VELOCITY
        self.safety_distance = Config.SAFETY_DISTANCE
        
        # 性能统计
        self.control_stats = {
            'teacher_actions': 0,
            'student_actions': 0,
            'safety_interventions': 0,
            'total_actions': 0
        }
    
    def set_control_mode(self, mode: ControlMode):
        """设置控制模式"""
        if mode == ControlMode.STUDENT and self.student_model is None:
            print("警告: 学生模型未加载，无法切换到学生模式")
            return False
        
        self.control_mode = mode
        print(f"控制模式已切换到: {mode.value}")
        return True
    
    def set_goal(self, goal_position: np.ndarray):
        """设置目标位置"""
        self.current_goal = goal_position.copy()
        print(f"目标位置已设置: {goal_position}")
    
    def get_current_state(self) -> dict:
        """获取当前状态"""
        position = np.array(self.airsim.get_position())
        velocity = np.array(self.airsim.get_velocity())
        depth_image = self.airsim.get_depth_image()
        imu_data = self.airsim.get_imu_data()
        
        # 处理点云
        all_points, obstacle_points = self.pointcloud_converter.process_depth_image(depth_image)
        
        state = {
            'position': position,
            'velocity': velocity,
            'depth_image': depth_image,
            'imu_data': imu_data,
            'pointcloud': all_points,
            'obstacle_points': obstacle_points,
            'goal': self.current_goal
        }
        
        return state
    
    def compute_teacher_action(self, state: dict) -> Tuple[np.ndarray, dict]:
        """计算教师动作"""
        action, info = self.teacher.get_teaching_action(
            state['position'],
            state['velocity'],
            state['goal'],
            state['obstacle_points']
        )
        
        self.control_stats['teacher_actions'] += 1
        return action, info
    
    def compute_student_action(self, state: dict) -> np.ndarray:
        """计算学生动作"""
        if self.student_model is None:
            raise ValueError("学生模型未加载")
        
        # 准备输入数据
        depth_image = state['depth_image']
        imu_data = self._prepare_imu_vector(state['imu_data'])
        
        # 预测动作
        action = self.student_model.predict(depth_image, imu_data)
        
        self.control_stats['student_actions'] += 1
        return action
    
    def _prepare_imu_vector(self, imu_data: dict) -> np.ndarray:
        """准备IMU数据向量"""
        if not imu_data:
            return np.zeros(10)
        
        try:
            imu_vector = np.array([
                imu_data['linear_acceleration']['x'],
                imu_data['linear_acceleration']['y'],
                imu_data['linear_acceleration']['z'],
                imu_data['angular_velocity']['x'],
                imu_data['angular_velocity']['y'],
                imu_data['angular_velocity']['z'],
                imu_data['orientation']['w'],
                imu_data['orientation']['x'],
                imu_data['orientation']['y'],
                imu_data['orientation']['z']
            ])
            return imu_vector
        except KeyError:
            return np.zeros(10)
    
    def safety_check(self, action: np.ndarray, state: dict) -> Tuple[np.ndarray, bool]:
        """
        安全检查和干预
        
        Args:
            action: 原始动作
            state: 当前状态
            
        Returns:
            safe_action: 安全动作
            intervention: 是否进行了安全干预
        """
        intervention = False
        safe_action = action.copy()
        
        # 检查速度限制
        action_magnitude = np.linalg.norm(action)
        if action_magnitude > self.max_velocity:
            safe_action = action / action_magnitude * self.max_velocity
            intervention = True
        
        # 检查碰撞风险
        if len(state['obstacle_points']) > 0:
            min_distance = np.min(np.linalg.norm(
                state['obstacle_points'] - state['position'], axis=1
            ))
            
            if min_distance < self.safety_distance:
                # 紧急制动或避障
                safe_action = np.array([0.0, 0.0, 0.0])
                intervention = True
        
        if intervention:
            self.control_stats['safety_interventions'] += 1
        
        return safe_action, intervention
    
    def compute_action(self, state: dict) -> Tuple[np.ndarray, dict]:
        """
        根据当前模式计算动作
        
        Args:
            state: 当前状态
            
        Returns:
            action: 控制动作
            info: 额外信息
        """
        info = {'mode': self.control_mode.value}
        
        if self.control_mode == ControlMode.TEACHER:
            action, teacher_info = self.compute_teacher_action(state)
            info.update(teacher_info)
            
        elif self.control_mode == ControlMode.STUDENT:
            action = self.compute_student_action(state)
            
        elif self.control_mode == ControlMode.HYBRID:
            # 混合模式：根据情况选择教师或学生
            teacher_action, teacher_info = self.compute_teacher_action(state)
            
            if self.student_model and not teacher_info['collision_risk']:
                # 安全情况下使用学生模型
                action = self.compute_student_action(state)
                info['primary_controller'] = 'student'
            else:
                # 危险情况下使用教师模型
                action = teacher_action
                info['primary_controller'] = 'teacher'
            
            info.update(teacher_info)
            
        else:  # MANUAL
            action = np.array([0.0, 0.0, 0.0])  # 悬停
        
        # 安全检查
        safe_action, intervention = self.safety_check(action, state)
        info['safety_intervention'] = intervention
        info['original_action'] = action
        info['safe_action'] = safe_action
        
        self.control_stats['total_actions'] += 1
        
        return safe_action, info
    
    def execute_action(self, action: np.ndarray, duration: float = None):
        """执行动作"""
        if duration is None:
            duration = 1.0 / Config.CONTROL_FREQUENCY
        
        self.airsim.set_velocity(action[0], action[1], action[2], duration)
    
    def control_step(self) -> dict:
        """执行一步控制"""
        # 获取当前状态
        state = self.get_current_state()
        
        # 计算动作
        action, info = self.compute_action(state)
        
        # 执行动作
        self.execute_action(action)
        
        # 返回完整信息
        result = {
            'state': state,
            'action': action,
            'info': info,
            'stats': self.control_stats.copy()
        }
        
        return result
    
    def start_autonomous_flight(self, duration: float = 60.0):
        """开始自主飞行"""
        print(f"开始自主飞行，模式: {self.control_mode.value}")
        print(f"目标位置: {self.current_goal}")
        
        self.is_active = True
        start_time = time.time()
        
        try:
            while self.is_active and (time.time() - start_time) < duration:
                # 执行控制步骤
                result = self.control_step()
                
                # 检查是否到达目标
                distance_to_goal = np.linalg.norm(
                    result['state']['position'] - self.current_goal
                )
                
                if distance_to_goal < Config.GOAL_THRESHOLD:
                    print("已到达目标位置")
                    break
                
                # 控制频率
                time.sleep(1.0 / Config.CONTROL_FREQUENCY)
                
        except KeyboardInterrupt:
            print("用户中断飞行")
        except Exception as e:
            print(f"飞行过程中发生错误: {e}")
        finally:
            self.stop_flight()
    
    def stop_flight(self):
        """停止飞行"""
        self.is_active = False
        self.airsim.hover()
        print("飞行已停止")
        
        # 打印统计信息
        print("\n飞行统计:")
        for key, value in self.control_stats.items():
            print(f"  {key}: {value}")
    
    def emergency_stop(self):
        """紧急停止"""
        print("紧急停止!")
        self.is_active = False
        self.airsim.hover()
    
    def reset(self):
        """重置控制器"""
        self.airsim.reset()
        self.control_stats = {
            'teacher_actions': 0,
            'student_actions': 0,
            'safety_interventions': 0,
            'total_actions': 0
        }
        print("控制器已重置")
    
    def cleanup(self):
        """清理资源"""
        self.stop_flight()
        self.airsim.disconnect()

def create_controller(model_path: Optional[str] = None) -> DroneController:
    """创建控制器实例"""
    return DroneController(model_path=model_path)
