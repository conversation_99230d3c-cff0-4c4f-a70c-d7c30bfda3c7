"""
教师模型 - 人工势场避障算法
"""
import numpy as np
from typing import Tuple, List
from config import Config

class PotentialFieldTeacher:
    def __init__(self):
        """初始化人工势场教师模型"""
        self.attractive_gain = Config.ATTRACTIVE_GAIN
        self.repulsive_gain = Config.REPULSIVE_GAIN
        self.obstacle_threshold = Config.OBSTACLE_THRESHOLD
        self.max_velocity = Config.MAX_VELOCITY
        
    def attractive_force(self, current_pos: np.ndarray, goal_pos: np.ndarray) -> np.ndarray:
        """
        计算引力
        
        Args:
            current_pos: 当前位置 [x, y, z]
            goal_pos: 目标位置 [x, y, z]
            
        Returns:
            force: 引力向量 [fx, fy, fz]
        """
        # 计算位置差向量
        diff = goal_pos - current_pos
        distance = np.linalg.norm(diff)
        
        if distance < 1e-6:  # 避免除零
            return np.zeros(3)
        
        # 引力与距离成正比
        force = self.attractive_gain * diff
        
        return force
    
    def repulsive_force(self, current_pos: np.ndarray, obstacle_points: np.ndarray) -> np.ndarray:
        """
        计算斥力
        
        Args:
            current_pos: 当前位置 [x, y, z]
            obstacle_points: 障碍物点云 (N, 3)
            
        Returns:
            force: 总斥力向量 [fx, fy, fz]
        """
        if len(obstacle_points) == 0:
            return np.zeros(3)
        
        total_force = np.zeros(3)
        
        for obstacle_point in obstacle_points:
            # 计算到障碍物的向量
            diff = current_pos - obstacle_point
            distance = np.linalg.norm(diff)
            
            # 只考虑影响范围内的障碍物
            if distance < self.obstacle_threshold and distance > 1e-6:
                # 斥力与距离平方成反比
                force_magnitude = self.repulsive_gain * (1.0/distance - 1.0/self.obstacle_threshold) / (distance**2)
                force_direction = diff / distance
                force = force_magnitude * force_direction
                
                total_force += force
        
        return total_force
    
    def compute_velocity_command(self, current_pos: np.ndarray, 
                               goal_pos: np.ndarray, 
                               obstacle_points: np.ndarray) -> np.ndarray:
        """
        计算速度命令
        
        Args:
            current_pos: 当前位置 [x, y, z]
            goal_pos: 目标位置 [x, y, z]
            obstacle_points: 障碍物点云 (N, 3)
            
        Returns:
            velocity: 速度命令 [vx, vy, vz]
        """
        # 计算引力
        attractive_f = self.attractive_force(current_pos, goal_pos)
        
        # 计算斥力
        repulsive_f = self.repulsive_force(current_pos, obstacle_points)
        
        # 合力
        total_force = attractive_f + repulsive_f
        
        # 转换为速度命令（简单比例控制）
        velocity = total_force
        
        # 限制最大速度
        velocity_magnitude = np.linalg.norm(velocity)
        if velocity_magnitude > self.max_velocity:
            velocity = velocity / velocity_magnitude * self.max_velocity
        
        return velocity
    
    def is_goal_reached(self, current_pos: np.ndarray, goal_pos: np.ndarray) -> bool:
        """
        检查是否到达目标
        
        Args:
            current_pos: 当前位置
            goal_pos: 目标位置
            
        Returns:
            reached: 是否到达目标
        """
        distance = np.linalg.norm(goal_pos - current_pos)
        return distance < Config.GOAL_THRESHOLD
    
    def check_collision_risk(self, current_pos: np.ndarray, obstacle_points: np.ndarray) -> bool:
        """
        检查碰撞风险
        
        Args:
            current_pos: 当前位置
            obstacle_points: 障碍物点云
            
        Returns:
            collision_risk: 是否有碰撞风险
        """
        if len(obstacle_points) == 0:
            return False
        
        # 计算到最近障碍物的距离
        distances = np.linalg.norm(obstacle_points - current_pos, axis=1)
        min_distance = np.min(distances)
        
        return min_distance < Config.SAFETY_DISTANCE
    
    def generate_waypoints(self, start_pos: np.ndarray, 
                          goal_pos: np.ndarray, 
                          num_waypoints: int = 10) -> List[np.ndarray]:
        """
        生成路径点
        
        Args:
            start_pos: 起始位置
            goal_pos: 目标位置
            num_waypoints: 路径点数量
            
        Returns:
            waypoints: 路径点列表
        """
        waypoints = []
        
        for i in range(num_waypoints + 1):
            t = i / num_waypoints
            waypoint = start_pos + t * (goal_pos - start_pos)
            waypoints.append(waypoint)
        
        return waypoints
    
    def get_teaching_action(self, current_pos: np.ndarray,
                           current_vel: np.ndarray,
                           goal_pos: np.ndarray,
                           obstacle_points: np.ndarray,
                           dt: float = 0.1) -> Tuple[np.ndarray, dict]:
        """
        获取教师动作（用于模仿学习）
        
        Args:
            current_pos: 当前位置
            current_vel: 当前速度
            goal_pos: 目标位置
            obstacle_points: 障碍物点云
            dt: 时间步长
            
        Returns:
            action: 动作命令 [vx, vy, vz]
            info: 额外信息
        """
        # 计算速度命令
        velocity_cmd = self.compute_velocity_command(current_pos, goal_pos, obstacle_points)
        
        # 收集额外信息
        info = {
            'attractive_force': self.attractive_force(current_pos, goal_pos),
            'repulsive_force': self.repulsive_force(current_pos, obstacle_points),
            'goal_reached': self.is_goal_reached(current_pos, goal_pos),
            'collision_risk': self.check_collision_risk(current_pos, obstacle_points),
            'distance_to_goal': np.linalg.norm(goal_pos - current_pos),
            'num_obstacles': len(obstacle_points)
        }
        
        return velocity_cmd, info
