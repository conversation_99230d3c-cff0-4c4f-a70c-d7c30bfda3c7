"""
可视化模块 - 实时显示系统状态
"""
import matplotlib.pyplot as plt
import matplotlib.animation as animation
import numpy as np
import cv2
from typing import Dict, List, Optional
import threading
import time
from collections import deque

from config import Config

class RealTimeVisualizer:
    """实时可视化器"""
    
    def __init__(self, max_history: int = None):
        """
        初始化可视化器
        
        Args:
            max_history: 最大历史记录长度
        """
        if max_history is None:
            max_history = Config.PLOT_HISTORY_LENGTH
            
        self.max_history = max_history
        
        # 数据存储
        self.position_history = deque(maxlen=max_history)
        self.velocity_history = deque(maxlen=max_history)
        self.action_history = deque(maxlen=max_history)
        self.time_history = deque(maxlen=max_history)
        
        # 图像数据
        self.current_rgb_image = None
        self.current_depth_image = None
        self.current_pointcloud = None
        
        # 状态信息
        self.current_status = {}
        
        # 可视化窗口
        self.fig = None
        self.axes = None
        self.animation = None
        
        # 线程控制
        self.running = False
        self.update_thread = None
        
    def update_data(self, data: Dict):
        """
        更新可视化数据
        
        Args:
            data: 包含所有可视化数据的字典
        """
        current_time = time.time()
        
        # 更新历史数据
        if 'position' in data:
            self.position_history.append(data['position'])
        if 'velocity' in data:
            self.velocity_history.append(data['velocity'])
        if 'action' in data:
            self.action_history.append(data['action'])
        
        self.time_history.append(current_time)
        
        # 更新图像数据
        if 'rgb_image' in data:
            self.current_rgb_image = data['rgb_image']
        if 'depth_image' in data:
            self.current_depth_image = data['depth_image']
        if 'pointcloud' in data:
            self.current_pointcloud = data['pointcloud']
        
        # 更新状态信息
        if 'status' in data:
            self.current_status.update(data['status'])
    
    def setup_plots(self):
        """设置绘图窗口"""
        self.fig = plt.figure(figsize=(16, 12))
        
        # 创建子图
        gs = self.fig.add_gridspec(3, 4, hspace=0.3, wspace=0.3)
        
        # RGB图像
        self.ax_rgb = self.fig.add_subplot(gs[0, 0])
        self.ax_rgb.set_title('RGB Image')
        self.ax_rgb.axis('off')
        
        # 深度图像
        self.ax_depth = self.fig.add_subplot(gs[0, 1])
        self.ax_depth.set_title('Depth Image')
        self.ax_depth.axis('off')
        
        # 3D轨迹
        self.ax_3d = self.fig.add_subplot(gs[0, 2:], projection='3d')
        self.ax_3d.set_title('3D Trajectory')
        self.ax_3d.set_xlabel('X (m)')
        self.ax_3d.set_ylabel('Y (m)')
        self.ax_3d.set_zlabel('Z (m)')
        
        # 位置历史
        self.ax_pos = self.fig.add_subplot(gs[1, 0])
        self.ax_pos.set_title('Position History')
        self.ax_pos.set_xlabel('Time')
        self.ax_pos.set_ylabel('Position (m)')
        self.ax_pos.legend(['X', 'Y', 'Z'])
        
        # 速度历史
        self.ax_vel = self.fig.add_subplot(gs[1, 1])
        self.ax_vel.set_title('Velocity History')
        self.ax_vel.set_xlabel('Time')
        self.ax_vel.set_ylabel('Velocity (m/s)')
        self.ax_vel.legend(['Vx', 'Vy', 'Vz'])
        
        # 动作历史
        self.ax_action = self.fig.add_subplot(gs[1, 2])
        self.ax_action.set_title('Action History')
        self.ax_action.set_xlabel('Time')
        self.ax_action.set_ylabel('Action (m/s)')
        self.ax_action.legend(['Ax', 'Ay', 'Az'])
        
        # 状态信息
        self.ax_status = self.fig.add_subplot(gs[1, 3])
        self.ax_status.set_title('Status Information')
        self.ax_status.axis('off')
        
        # 点云可视化
        self.ax_pointcloud = self.fig.add_subplot(gs[2, :2], projection='3d')
        self.ax_pointcloud.set_title('Point Cloud')
        self.ax_pointcloud.set_xlabel('X (m)')
        self.ax_pointcloud.set_ylabel('Y (m)')
        self.ax_pointcloud.set_zlabel('Z (m)')
        
        # 控制面板
        self.ax_control = self.fig.add_subplot(gs[2, 2:])
        self.ax_control.set_title('Control Panel')
        self.ax_control.axis('off')
        
        plt.tight_layout()
    
    def update_plots(self, frame):
        """更新绘图"""
        try:
            # 清除所有子图
            for ax in [self.ax_pos, self.ax_vel, self.ax_action, self.ax_3d, 
                      self.ax_pointcloud, self.ax_status, self.ax_control]:
                ax.clear()
            
            # 更新RGB图像
            if self.current_rgb_image is not None:
                self.ax_rgb.clear()
                self.ax_rgb.imshow(self.current_rgb_image)
                self.ax_rgb.set_title('RGB Image')
                self.ax_rgb.axis('off')
            
            # 更新深度图像
            if self.current_depth_image is not None:
                self.ax_depth.clear()
                self.ax_depth.imshow(self.current_depth_image, cmap='viridis')
                self.ax_depth.set_title('Depth Image')
                self.ax_depth.axis('off')
            
            # 更新历史曲线
            if len(self.time_history) > 1:
                times = np.array(list(self.time_history))
                times = times - times[0]  # 相对时间
                
                # 位置历史
                if len(self.position_history) > 1:
                    positions = np.array(list(self.position_history))
                    self.ax_pos.plot(times, positions[:, 0], 'r-', label='X')
                    self.ax_pos.plot(times, positions[:, 1], 'g-', label='Y')
                    self.ax_pos.plot(times, positions[:, 2], 'b-', label='Z')
                    self.ax_pos.set_title('Position History')
                    self.ax_pos.set_xlabel('Time (s)')
                    self.ax_pos.set_ylabel('Position (m)')
                    self.ax_pos.legend()
                    self.ax_pos.grid(True)
                
                # 速度历史
                if len(self.velocity_history) > 1:
                    velocities = np.array(list(self.velocity_history))
                    self.ax_vel.plot(times, velocities[:, 0], 'r-', label='Vx')
                    self.ax_vel.plot(times, velocities[:, 1], 'g-', label='Vy')
                    self.ax_vel.plot(times, velocities[:, 2], 'b-', label='Vz')
                    self.ax_vel.set_title('Velocity History')
                    self.ax_vel.set_xlabel('Time (s)')
                    self.ax_vel.set_ylabel('Velocity (m/s)')
                    self.ax_vel.legend()
                    self.ax_vel.grid(True)
                
                # 动作历史
                if len(self.action_history) > 1:
                    actions = np.array(list(self.action_history))
                    self.ax_action.plot(times, actions[:, 0], 'r-', label='Ax')
                    self.ax_action.plot(times, actions[:, 1], 'g-', label='Ay')
                    self.ax_action.plot(times, actions[:, 2], 'b-', label='Az')
                    self.ax_action.set_title('Action History')
                    self.ax_action.set_xlabel('Time (s)')
                    self.ax_action.set_ylabel('Action (m/s)')
                    self.ax_action.legend()
                    self.ax_action.grid(True)
                
                # 3D轨迹
                if len(self.position_history) > 1:
                    positions = np.array(list(self.position_history))
                    self.ax_3d.plot(positions[:, 0], positions[:, 1], positions[:, 2], 'b-', linewidth=2)
                    if len(positions) > 0:
                        # 当前位置
                        self.ax_3d.scatter(positions[-1, 0], positions[-1, 1], positions[-1, 2], 
                                         c='red', s=100, marker='o')
                    self.ax_3d.set_title('3D Trajectory')
                    self.ax_3d.set_xlabel('X (m)')
                    self.ax_3d.set_ylabel('Y (m)')
                    self.ax_3d.set_zlabel('Z (m)')
            
            # 更新点云
            if self.current_pointcloud is not None and len(self.current_pointcloud) > 0:
                points = self.current_pointcloud
                self.ax_pointcloud.scatter(points[:, 0], points[:, 1], points[:, 2], 
                                         c=points[:, 2], cmap='viridis', s=1)
                self.ax_pointcloud.set_title('Point Cloud')
                self.ax_pointcloud.set_xlabel('X (m)')
                self.ax_pointcloud.set_ylabel('Y (m)')
                self.ax_pointcloud.set_zlabel('Z (m)')
            
            # 更新状态信息
            status_text = ""
            for key, value in self.current_status.items():
                if isinstance(value, float):
                    status_text += f"{key}: {value:.3f}\n"
                else:
                    status_text += f"{key}: {value}\n"
            
            self.ax_status.text(0.1, 0.9, status_text, transform=self.ax_status.transAxes,
                              fontsize=10, verticalalignment='top', fontfamily='monospace')
            self.ax_status.set_title('Status Information')
            
            # 控制面板
            control_text = "Controls:\n"
            control_text += "- Press 'q' to quit\n"
            control_text += "- Press 'r' to reset\n"
            control_text += "- Press 's' to save screenshot\n"
            
            self.ax_control.text(0.1, 0.9, control_text, transform=self.ax_control.transAxes,
                               fontsize=10, verticalalignment='top')
            self.ax_control.set_title('Control Panel')
            
        except Exception as e:
            print(f"绘图更新错误: {e}")
    
    def start_visualization(self):
        """开始可视化"""
        self.running = True
        self.setup_plots()
        
        # 创建动画
        self.animation = animation.FuncAnimation(
            self.fig, self.update_plots, interval=1000//Config.VISUALIZATION_UPDATE_RATE,
            blit=False, cache_frame_data=False
        )
        
        plt.show()
    
    def stop_visualization(self):
        """停止可视化"""
        self.running = False
        if self.animation:
            self.animation.event_source.stop()
        plt.close('all')
    
    def save_screenshot(self, filename: str = None):
        """保存截图"""
        if filename is None:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}.png"
        
        if self.fig:
            self.fig.savefig(filename, dpi=300, bbox_inches='tight')
            print(f"截图已保存: {filename}")

class DataLogger:
    """数据记录器"""
    
    def __init__(self, log_file: str = None):
        """
        初始化数据记录器
        
        Args:
            log_file: 日志文件路径
        """
        if log_file is None:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            log_file = f"flight_log_{timestamp}.txt"
        
        self.log_file = log_file
        self.log_data = []
        
    def log(self, data: Dict):
        """记录数据"""
        timestamp = time.time()
        log_entry = {
            'timestamp': timestamp,
            **data
        }
        self.log_data.append(log_entry)
    
    def save_log(self):
        """保存日志"""
        with open(self.log_file, 'w') as f:
            for entry in self.log_data:
                f.write(f"{entry}\n")
        print(f"日志已保存: {self.log_file}")

def create_visualizer(max_history: int = None) -> RealTimeVisualizer:
    """创建可视化器实例"""
    return RealTimeVisualizer(max_history=max_history)
