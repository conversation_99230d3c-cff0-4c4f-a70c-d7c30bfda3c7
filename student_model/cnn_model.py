"""
学生模型 - CNN+FC网络架构
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Tuple
from config import Config

class DepthCNN(nn.Module):
    """深度图像CNN特征提取器"""

    def __init__(self, input_channels: int = 1):
        super(DepthCNN, self).__init__()

        # 卷积层
        self.conv1 = nn.Conv2d(input_channels, 32, kernel_size=8, stride=4, padding=2)
        self.conv2 = nn.Conv2d(32, 64, kernel_size=4, stride=2, padding=1)
        self.conv3 = nn.Conv2d(64, 128, kernel_size=3, stride=1, padding=1)
        self.conv4 = nn.Conv2d(128, 256, kernel_size=3, stride=1, padding=1)

        # 批归一化
        self.bn1 = nn.BatchNorm2d(32)
        self.bn2 = nn.BatchNorm2d(64)
        self.bn3 = nn.BatchNorm2d(128)
        self.bn4 = nn.BatchNorm2d(256)

        # Dropout
        self.dropout = nn.Dropout2d(0.3)

        # 计算卷积后的特征图尺寸
        self._calculate_conv_output_size()

    def _calculate_conv_output_size(self):
        """计算卷积层输出尺寸"""
        # 假设输入尺寸为 (1, 144, 256)
        x = torch.zeros(1, 1, Config.DEPTH_HEIGHT, Config.DEPTH_WIDTH)
        x = self._forward_conv(x)
        self.conv_output_size = x.view(x.size(0), -1).size(1)

    def _forward_conv(self, x):
        """卷积层前向传播"""
        x = F.relu(self.bn1(self.conv1(x)))
        x = F.relu(self.bn2(self.conv2(x)))
        x = F.relu(self.bn3(self.conv3(x)))
        x = F.relu(self.bn4(self.conv4(x)))
        x = self.dropout(x)
        return x

    def forward(self, x):
        """前向传播"""
        x = self._forward_conv(x)
        x = x.view(x.size(0), -1)  # 展平
        return x

class IMUEncoder(nn.Module):
    """IMU数据编码器"""

    def __init__(self, input_dim: int = 10, hidden_dim: int = 64):
        super(IMUEncoder, self).__init__()

        self.fc1 = nn.Linear(input_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, hidden_dim)
        self.bn1 = nn.BatchNorm1d(hidden_dim)
        self.dropout = nn.Dropout(0.2)

    def forward(self, x):
        """前向传播"""
        x = self.fc1(x)
        if x.size(0) > 1:  # 只有批大小>1时才使用BatchNorm
            x = self.bn1(x)
        x = F.relu(x)
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        return x

class ObstacleAvoidanceNet(nn.Module):
    """完整的避障网络"""

    def __init__(self, use_imu: bool = True):
        super(ObstacleAvoidanceNet, self).__init__()

        self.use_imu = use_imu

        # 深度图像CNN
        self.depth_cnn = DepthCNN(Config.CNN_INPUT_CHANNELS)

        # IMU编码器
        if self.use_imu:
            self.imu_encoder = IMUEncoder()
            fusion_input_dim = self.depth_cnn.conv_output_size + 64
        else:
            fusion_input_dim = self.depth_cnn.conv_output_size

        # 融合层
        self.fusion_fc1 = nn.Linear(fusion_input_dim, Config.CNN_HIDDEN_DIM)
        self.fusion_fc2 = nn.Linear(Config.CNN_HIDDEN_DIM, Config.FC_HIDDEN_DIM)

        # 输出层
        self.output_fc = nn.Linear(Config.FC_HIDDEN_DIM, Config.OUTPUT_DIM)

        # 批归一化和Dropout
        self.bn_fusion1 = nn.BatchNorm1d(Config.CNN_HIDDEN_DIM)
        self.bn_fusion2 = nn.BatchNorm1d(Config.FC_HIDDEN_DIM)
        self.dropout = nn.Dropout(0.3)

    def forward(self, depth_image, imu_data=None):
        """
        前向传播

        Args:
            depth_image: 深度图像 (B, 1, H, W)
            imu_data: IMU数据 (B, 9) - [ax, ay, az, gx, gy, gz, qw, qx, qy, qz]

        Returns:
            velocity: 速度命令 (B, 3) - [vx, vy, vz]
        """
        # 深度图像特征提取
        depth_features = self.depth_cnn(depth_image)

        # 特征融合
        if self.use_imu and imu_data is not None:
            imu_features = self.imu_encoder(imu_data)
            fused_features = torch.cat([depth_features, imu_features], dim=1)
        else:
            fused_features = depth_features

        # 全连接层
        x = self.fusion_fc1(fused_features)
        if x.size(0) > 1:  # 只有批大小>1时才使用BatchNorm
            x = self.bn_fusion1(x)
        x = F.relu(x)
        x = self.dropout(x)

        x = self.fusion_fc2(x)
        if x.size(0) > 1:  # 只有批大小>1时才使用BatchNorm
            x = self.bn_fusion2(x)
        x = F.relu(x)
        x = self.dropout(x)

        # 输出速度命令
        velocity = self.output_fc(x)

        # 限制输出范围
        velocity = torch.tanh(velocity) * Config.MAX_VELOCITY

        return velocity

    def predict(self, depth_image: np.ndarray, imu_data: np.ndarray = None) -> np.ndarray:
        """
        预测函数（用于推理）

        Args:
            depth_image: 深度图像 (H, W)
            imu_data: IMU数据 (9,)

        Returns:
            velocity: 速度命令 (3,)
        """
        self.eval()

        with torch.no_grad():
            # 预处理深度图像
            if len(depth_image.shape) == 2:
                depth_image = depth_image[np.newaxis, np.newaxis, :, :]  # (1, 1, H, W)
            elif len(depth_image.shape) == 3:
                depth_image = depth_image[np.newaxis, :, :, :]  # (1, C, H, W)

            depth_tensor = torch.FloatTensor(depth_image)

            # 预处理IMU数据
            imu_tensor = None
            if self.use_imu and imu_data is not None:
                if len(imu_data.shape) == 1:
                    imu_data = imu_data[np.newaxis, :]  # (1, 9)
                imu_tensor = torch.FloatTensor(imu_data)

            # 前向传播
            velocity = self.forward(depth_tensor, imu_tensor)

            return velocity.cpu().numpy().squeeze()

def create_model(use_imu: bool = True) -> ObstacleAvoidanceNet:
    """创建模型实例"""
    model = ObstacleAvoidanceNet(use_imu=use_imu)
    return model

def load_model(model_path: str, use_imu: bool = True) -> ObstacleAvoidanceNet:
    """加载预训练模型"""
    model = create_model(use_imu=use_imu)
    model.load_state_dict(torch.load(model_path, map_location='cpu'))
    return model

def save_model(model: ObstacleAvoidanceNet, model_path: str):
    """保存模型"""
    torch.save(model.state_dict(), model_path)

def count_parameters(model: ObstacleAvoidanceNet) -> int:
    """计算模型参数数量"""
    return sum(p.numel() for p in model.parameters() if p.requires_grad)
