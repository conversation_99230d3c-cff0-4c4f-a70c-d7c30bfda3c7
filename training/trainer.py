"""
模型训练模块
"""
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.tensorboard import SummaryWriter
import numpy as np
import os
import time
from typing import Dict, List, Tuple
from tqdm import tqdm

from student_model import ObstacleAvoidanceNet, save_model
from .dataset import create_data_loaders
from config import Config

class ImitationLearningTrainer:
    """模仿学习训练器"""
    
    def __init__(self, model: ObstacleAvoidanceNet, device: str = 'cpu'):
        """
        初始化训练器
        
        Args:
            model: 学生模型
            device: 训练设备
        """
        self.model = model
        self.device = device
        self.model.to(device)
        
        # 损失函数
        self.criterion = nn.MSELoss()
        
        # 优化器
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=Config.LEARNING_RATE,
            weight_decay=1e-4
        )
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer,
            mode='min',
            factor=0.5,
            patience=10,
            verbose=True
        )
        
        # TensorBoard记录器
        self.writer = SummaryWriter(Config.LOG_DIR)
        
        # 训练历史
        self.train_losses = []
        self.val_losses = []
        self.best_val_loss = float('inf')
        
    def train_epoch(self, train_loader) -> float:
        """
        训练一个epoch
        
        Args:
            train_loader: 训练数据加载器
            
        Returns:
            avg_loss: 平均损失
        """
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        progress_bar = tqdm(train_loader, desc="Training")
        
        for batch_idx, (depth_images, imu_data, actions) in enumerate(progress_bar):
            # 移动数据到设备
            depth_images = depth_images.to(self.device)
            actions = actions.to(self.device)
            
            if imu_data is not None:
                imu_data = imu_data.to(self.device)
            
            # 前向传播
            self.optimizer.zero_grad()
            predictions = self.model(depth_images, imu_data)
            
            # 计算损失
            loss = self.criterion(predictions, actions)
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            # 更新参数
            self.optimizer.step()
            
            # 记录损失
            total_loss += loss.item()
            num_batches += 1
            
            # 更新进度条
            progress_bar.set_postfix({'Loss': f'{loss.item():.6f}'})
        
        avg_loss = total_loss / num_batches
        return avg_loss
    
    def validate_epoch(self, val_loader) -> Tuple[float, Dict]:
        """
        验证一个epoch
        
        Args:
            val_loader: 验证数据加载器
            
        Returns:
            avg_loss: 平均损失
            metrics: 评估指标
        """
        self.model.eval()
        total_loss = 0.0
        num_batches = 0
        
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for depth_images, imu_data, actions in val_loader:
                # 移动数据到设备
                depth_images = depth_images.to(self.device)
                actions = actions.to(self.device)
                
                if imu_data is not None:
                    imu_data = imu_data.to(self.device)
                
                # 前向传播
                predictions = self.model(depth_images, imu_data)
                
                # 计算损失
                loss = self.criterion(predictions, actions)
                
                total_loss += loss.item()
                num_batches += 1
                
                # 收集预测和目标
                all_predictions.append(predictions.cpu().numpy())
                all_targets.append(actions.cpu().numpy())
        
        avg_loss = total_loss / num_batches
        
        # 计算评估指标
        all_predictions = np.concatenate(all_predictions, axis=0)
        all_targets = np.concatenate(all_targets, axis=0)
        
        metrics = self._compute_metrics(all_predictions, all_targets)
        
        return avg_loss, metrics
    
    def _compute_metrics(self, predictions: np.ndarray, targets: np.ndarray) -> Dict:
        """
        计算评估指标
        
        Args:
            predictions: 预测值 (N, 3)
            targets: 目标值 (N, 3)
            
        Returns:
            metrics: 评估指标字典
        """
        # 均方误差
        mse = np.mean((predictions - targets) ** 2)
        
        # 平均绝对误差
        mae = np.mean(np.abs(predictions - targets))
        
        # 各维度的误差
        mse_per_dim = np.mean((predictions - targets) ** 2, axis=0)
        mae_per_dim = np.mean(np.abs(predictions - targets), axis=0)
        
        # 相关系数
        correlations = []
        for i in range(predictions.shape[1]):
            corr = np.corrcoef(predictions[:, i], targets[:, i])[0, 1]
            correlations.append(corr if not np.isnan(corr) else 0.0)
        
        metrics = {
            'mse': mse,
            'mae': mae,
            'mse_vx': mse_per_dim[0],
            'mse_vy': mse_per_dim[1],
            'mse_vz': mse_per_dim[2],
            'mae_vx': mae_per_dim[0],
            'mae_vy': mae_per_dim[1],
            'mae_vz': mae_per_dim[2],
            'corr_vx': correlations[0],
            'corr_vy': correlations[1],
            'corr_vz': correlations[2]
        }
        
        return metrics
    
    def train(self, dataset: List[Dict], 
              num_epochs: int = None,
              train_ratio: float = 0.8,
              save_best: bool = True) -> Dict:
        """
        完整训练流程
        
        Args:
            dataset: 训练数据集
            num_epochs: 训练轮数
            train_ratio: 训练集比例
            save_best: 是否保存最佳模型
            
        Returns:
            training_history: 训练历史
        """
        if num_epochs is None:
            num_epochs = Config.NUM_EPOCHS
        
        print(f"开始训练，数据集大小: {len(dataset)}")
        print(f"训练轮数: {num_epochs}")
        print(f"设备: {self.device}")
        
        # 创建数据加载器
        train_loader, val_loader = create_data_loaders(
            dataset, 
            train_ratio=train_ratio,
            use_imu=self.model.use_imu
        )
        
        print(f"训练集大小: {len(train_loader.dataset)}")
        print(f"验证集大小: {len(val_loader.dataset)}")
        
        # 训练循环
        for epoch in range(num_epochs):
            print(f"\nEpoch {epoch+1}/{num_epochs}")
            
            # 训练
            train_loss = self.train_epoch(train_loader)
            
            # 验证
            val_loss, val_metrics = self.validate_epoch(val_loader)
            
            # 更新学习率
            self.scheduler.step(val_loss)
            
            # 记录历史
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            
            # TensorBoard记录
            self.writer.add_scalar('Loss/Train', train_loss, epoch)
            self.writer.add_scalar('Loss/Validation', val_loss, epoch)
            self.writer.add_scalar('Learning_Rate', self.optimizer.param_groups[0]['lr'], epoch)
            
            for metric_name, metric_value in val_metrics.items():
                self.writer.add_scalar(f'Metrics/{metric_name}', metric_value, epoch)
            
            # 打印结果
            print(f"Train Loss: {train_loss:.6f}")
            print(f"Val Loss: {val_loss:.6f}")
            print(f"Val MAE: {val_metrics['mae']:.6f}")
            print(f"Val Correlations: vx={val_metrics['corr_vx']:.3f}, "
                  f"vy={val_metrics['corr_vy']:.3f}, vz={val_metrics['corr_vz']:.3f}")
            
            # 保存最佳模型
            if save_best and val_loss < self.best_val_loss:
                self.best_val_loss = val_loss
                best_model_path = os.path.join(Config.MODEL_DIR, 'best_model.pth')
                save_model(self.model, best_model_path)
                print(f"保存最佳模型到: {best_model_path}")
        
        # 保存最终模型
        final_model_path = os.path.join(Config.MODEL_DIR, 'final_model.pth')
        save_model(self.model, final_model_path)
        print(f"保存最终模型到: {final_model_path}")
        
        # 关闭TensorBoard
        self.writer.close()
        
        # 返回训练历史
        training_history = {
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'best_val_loss': self.best_val_loss
        }
        
        return training_history
    
    def save_training_history(self, filepath: str):
        """保存训练历史"""
        history = {
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'best_val_loss': self.best_val_loss
        }
        
        torch.save(history, filepath)
        print(f"训练历史已保存到: {filepath}")

def create_trainer(use_imu: bool = True, device: str = None) -> ImitationLearningTrainer:
    """
    创建训练器
    
    Args:
        use_imu: 是否使用IMU数据
        device: 训练设备
        
    Returns:
        trainer: 训练器实例
    """
    if device is None:
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # 创建模型
    from student_model import create_model
    model = create_model(use_imu=use_imu)
    
    # 创建训练器
    trainer = ImitationLearningTrainer(model, device)
    
    return trainer
