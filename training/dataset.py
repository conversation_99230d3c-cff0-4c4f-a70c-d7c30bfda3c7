"""
训练数据集处理模块
"""
import torch
from torch.utils.data import Dataset, DataLoader
import numpy as np
from typing import List, Dict, Tuple
from config import Config

class ObstacleAvoidanceDataset(Dataset):
    """避障数据集类"""
    
    def __init__(self, data_samples: List[Dict], use_imu: bool = True, normalize: bool = True):
        """
        初始化数据集
        
        Args:
            data_samples: 数据样本列表
            use_imu: 是否使用IMU数据
            normalize: 是否归一化数据
        """
        self.data_samples = data_samples
        self.use_imu = use_imu
        self.normalize = normalize
        
        # 计算归一化参数
        if self.normalize:
            self._compute_normalization_params()
    
    def _compute_normalization_params(self):
        """计算归一化参数"""
        # 收集所有深度图像
        depth_images = []
        imu_data = []
        actions = []
        
        for sample in self.data_samples:
            depth_images.append(sample['depth_image'])
            if self.use_imu:
                imu_data.append(sample['imu_data'])
            actions.append(sample['teacher_action'])
        
        # 深度图像归一化参数
        depth_images = np.array(depth_images)
        self.depth_mean = np.mean(depth_images)
        self.depth_std = np.std(depth_images)
        
        # IMU数据归一化参数
        if self.use_imu and imu_data:
            imu_data = np.array(imu_data)
            self.imu_mean = np.mean(imu_data, axis=0)
            self.imu_std = np.std(imu_data, axis=0)
            # 避免除零
            self.imu_std[self.imu_std < 1e-6] = 1.0
        
        # 动作归一化参数
        actions = np.array(actions)
        self.action_mean = np.mean(actions, axis=0)
        self.action_std = np.std(actions, axis=0)
        # 避免除零
        self.action_std[self.action_std < 1e-6] = 1.0
    
    def __len__(self) -> int:
        """返回数据集大小"""
        return len(self.data_samples)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        获取单个样本
        
        Args:
            idx: 样本索引
            
        Returns:
            depth_image: 深度图像张量 (1, H, W)
            imu_data: IMU数据张量 (10,) 或 None
            action: 动作张量 (3,)
        """
        sample = self.data_samples[idx]
        
        # 处理深度图像
        depth_image = sample['depth_image'].astype(np.float32)
        
        # 归一化深度图像
        if self.normalize:
            depth_image = (depth_image - self.depth_mean) / self.depth_std
        
        # 添加通道维度
        depth_image = depth_image[np.newaxis, :, :]  # (1, H, W)
        depth_tensor = torch.FloatTensor(depth_image)
        
        # 处理IMU数据
        imu_tensor = None
        if self.use_imu:
            imu_data = sample['imu_data'].astype(np.float32)
            
            # 归一化IMU数据
            if self.normalize:
                imu_data = (imu_data - self.imu_mean) / self.imu_std
            
            imu_tensor = torch.FloatTensor(imu_data)
        
        # 处理动作
        action = sample['teacher_action'].astype(np.float32)
        
        # 归一化动作（可选）
        if self.normalize:
            action = (action - self.action_mean) / self.action_std
        
        action_tensor = torch.FloatTensor(action)
        
        return depth_tensor, imu_tensor, action_tensor
    
    def get_normalization_params(self) -> Dict:
        """获取归一化参数"""
        if not self.normalize:
            return {}
        
        params = {
            'depth_mean': self.depth_mean,
            'depth_std': self.depth_std,
            'action_mean': self.action_mean,
            'action_std': self.action_std
        }
        
        if self.use_imu:
            params.update({
                'imu_mean': self.imu_mean,
                'imu_std': self.imu_std
            })
        
        return params

def create_data_loaders(dataset: List[Dict], 
                       train_ratio: float = 0.8,
                       batch_size: int = None,
                       use_imu: bool = True,
                       normalize: bool = True) -> Tuple[DataLoader, DataLoader]:
    """
    创建训练和验证数据加载器
    
    Args:
        dataset: 原始数据集
        train_ratio: 训练集比例
        batch_size: 批大小
        use_imu: 是否使用IMU数据
        normalize: 是否归一化
        
    Returns:
        train_loader: 训练数据加载器
        val_loader: 验证数据加载器
    """
    if batch_size is None:
        batch_size = Config.BATCH_SIZE
    
    # 划分训练和验证集
    num_samples = len(dataset)
    num_train = int(num_samples * train_ratio)
    
    # 随机打乱数据
    indices = np.random.permutation(num_samples)
    train_indices = indices[:num_train]
    val_indices = indices[num_train:]
    
    train_samples = [dataset[i] for i in train_indices]
    val_samples = [dataset[i] for i in val_indices]
    
    # 创建数据集对象
    train_dataset = ObstacleAvoidanceDataset(train_samples, use_imu=use_imu, normalize=normalize)
    val_dataset = ObstacleAvoidanceDataset(val_samples, use_imu=use_imu, normalize=normalize)
    
    # 使用训练集的归一化参数
    if normalize:
        norm_params = train_dataset.get_normalization_params()
        val_dataset.depth_mean = norm_params['depth_mean']
        val_dataset.depth_std = norm_params['depth_std']
        val_dataset.action_mean = norm_params['action_mean']
        val_dataset.action_std = norm_params['action_std']
        
        if use_imu:
            val_dataset.imu_mean = norm_params['imu_mean']
            val_dataset.imu_std = norm_params['imu_std']
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=4,
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    return train_loader, val_loader

def collate_fn(batch):
    """
    自定义批处理函数
    
    Args:
        batch: 批数据
        
    Returns:
        batched_data: 批处理后的数据
    """
    depth_images = []
    imu_data = []
    actions = []
    
    for depth, imu, action in batch:
        depth_images.append(depth)
        if imu is not None:
            imu_data.append(imu)
        actions.append(action)
    
    # 堆叠张量
    depth_batch = torch.stack(depth_images)
    action_batch = torch.stack(actions)
    
    imu_batch = None
    if imu_data:
        imu_batch = torch.stack(imu_data)
    
    return depth_batch, imu_batch, action_batch
