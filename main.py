#!/usr/bin/env python3
"""
深度图像模仿学习无人机避障系统 - 主程序
"""
import argparse
import numpy as np
import os
import sys
import threading
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from data_collection import DataCollector
from training import create_trainer
from control import DroneController, ControlMode
from visualization import create_visualizer, DataLogger
from utils import split_dataset, save_dataset_split, visualize_dataset_statistics

def collect_data(args):
    """数据采集模式"""
    print("=== 数据采集模式 ===")
    
    # 创建数据采集器
    collector = DataCollector()
    
    try:
        # 生成目标位置
        if args.num_trajectories:
            num_trajectories = args.num_trajectories
        else:
            num_trajectories = 10
        
        print(f"将采集 {num_trajectories} 条轨迹")
        
        # 采集数据
        dataset = collector.collect_dataset(num_trajectories=num_trajectories)
        
        # 保存数据
        if args.output:
            filepath = collector.save_dataset(args.output)
        else:
            filepath = collector.save_dataset()
        
        # 显示统计信息
        stats = collector.get_dataset_statistics()
        print("\n数据集统计信息:")
        print(f"总样本数: {stats['num_samples']}")
        
        # 可视化统计信息
        if args.visualize:
            visualize_dataset_statistics(dataset)
        
        return filepath
        
    except Exception as e:
        print(f"数据采集失败: {e}")
        return None
    finally:
        collector.cleanup()

def train_model(args):
    """模型训练模式"""
    print("=== 模型训练模式 ===")
    
    # 加载数据集
    if not args.dataset:
        print("错误: 请指定数据集文件路径")
        return None
    
    if not os.path.exists(args.dataset):
        print(f"错误: 数据集文件不存在: {args.dataset}")
        return None
    
    # 加载数据
    import pickle
    with open(args.dataset, 'rb') as f:
        dataset = pickle.load(f)
    
    print(f"数据集已加载，样本数: {len(dataset)}")
    
    # 划分数据集
    train_set, val_set, test_set = split_dataset(dataset, train_ratio=0.8, val_ratio=0.1, test_ratio=0.1)
    
    # 保存划分后的数据集
    save_dataset_split(train_set, val_set, test_set)
    
    # 创建训练器
    use_imu = not args.no_imu
    trainer = create_trainer(use_imu=use_imu)
    
    print(f"模型参数数量: {sum(p.numel() for p in trainer.model.parameters() if p.requires_grad)}")
    
    # 开始训练
    num_epochs = args.epochs if args.epochs else Config.NUM_EPOCHS
    
    training_history = trainer.train(
        dataset=train_set,
        num_epochs=num_epochs,
        train_ratio=1.0,  # 已经划分过了
        save_best=True
    )
    
    # 保存训练历史
    history_path = os.path.join(Config.MODEL_DIR, 'training_history.pth')
    trainer.save_training_history(history_path)
    
    print("训练完成!")
    return os.path.join(Config.MODEL_DIR, 'best_model.pth')

def run_flight(args):
    """飞行控制模式"""
    print("=== 飞行控制模式 ===")
    
    # 创建控制器
    model_path = args.model if args.model else None
    controller = DroneController(model_path=model_path)
    
    # 设置控制模式
    if args.mode == 'teacher':
        controller.set_control_mode(ControlMode.TEACHER)
    elif args.mode == 'student':
        if model_path is None:
            print("错误: 学生模式需要指定模型路径")
            return
        controller.set_control_mode(ControlMode.STUDENT)
    elif args.mode == 'hybrid':
        if model_path is None:
            print("错误: 混合模式需要指定模型路径")
            return
        controller.set_control_mode(ControlMode.HYBRID)
    else:
        controller.set_control_mode(ControlMode.MANUAL)
    
    # 设置目标位置
    if args.goal:
        goal = np.array(args.goal)
        controller.set_goal(goal)
    
    # 创建可视化器
    visualizer = None
    data_logger = None
    
    if args.visualize:
        visualizer = create_visualizer()
        data_logger = DataLogger()
        
        # 在单独线程中启动可视化
        def start_visualization():
            visualizer.start_visualization()
        
        vis_thread = threading.Thread(target=start_visualization)
        vis_thread.daemon = True
        vis_thread.start()
    
    try:
        # 开始飞行
        duration = args.duration if args.duration else 60.0
        
        if args.visualize:
            # 带可视化的飞行循环
            start_time = time.time()
            
            while controller.is_active and (time.time() - start_time) < duration:
                # 执行控制步骤
                result = controller.control_step()
                
                # 更新可视化
                vis_data = {
                    'position': result['state']['position'],
                    'velocity': result['state']['velocity'],
                    'action': result['action'],
                    'rgb_image': controller.airsim.get_rgb_image(),
                    'depth_image': result['state']['depth_image'],
                    'pointcloud': result['state']['pointcloud'],
                    'status': {
                        'mode': result['info']['mode'],
                        'distance_to_goal': np.linalg.norm(
                            result['state']['position'] - result['state']['goal']
                        ),
                        'num_obstacles': len(result['state']['obstacle_points']),
                        'safety_intervention': result['info']['safety_intervention']
                    }
                }
                
                visualizer.update_data(vis_data)
                data_logger.log(result)
                
                # 检查是否到达目标
                distance_to_goal = np.linalg.norm(
                    result['state']['position'] - result['state']['goal']
                )
                
                if distance_to_goal < Config.GOAL_THRESHOLD:
                    print("已到达目标位置")
                    break
                
                time.sleep(1.0 / Config.CONTROL_FREQUENCY)
        else:
            # 简单飞行
            controller.start_autonomous_flight(duration)
        
        # 保存日志
        if data_logger:
            data_logger.save_log()
        
    except KeyboardInterrupt:
        print("用户中断飞行")
    except Exception as e:
        print(f"飞行过程中发生错误: {e}")
    finally:
        controller.cleanup()
        if visualizer:
            visualizer.stop_visualization()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="深度图像模仿学习无人机避障系统")
    
    # 子命令
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 数据采集命令
    collect_parser = subparsers.add_parser('collect', help='采集训练数据')
    collect_parser.add_argument('--num-trajectories', type=int, default=10,
                               help='采集轨迹数量')
    collect_parser.add_argument('--output', type=str,
                               help='输出文件名')
    collect_parser.add_argument('--visualize', action='store_true',
                               help='显示数据统计可视化')
    
    # 训练命令
    train_parser = subparsers.add_parser('train', help='训练学生模型')
    train_parser.add_argument('--dataset', type=str, required=True,
                             help='训练数据集路径')
    train_parser.add_argument('--epochs', type=int,
                             help='训练轮数')
    train_parser.add_argument('--no-imu', action='store_true',
                             help='不使用IMU数据')
    
    # 飞行命令
    flight_parser = subparsers.add_parser('fly', help='执行飞行任务')
    flight_parser.add_argument('--mode', type=str, 
                              choices=['teacher', 'student', 'hybrid', 'manual'],
                              default='teacher',
                              help='控制模式')
    flight_parser.add_argument('--model', type=str,
                              help='学生模型路径')
    flight_parser.add_argument('--goal', type=float, nargs=3,
                              default=[10.0, 0.0, -5.0],
                              help='目标位置 [x, y, z]')
    flight_parser.add_argument('--duration', type=float, default=60.0,
                              help='飞行时长(秒)')
    flight_parser.add_argument('--visualize', action='store_true',
                              help='启用实时可视化')
    
    # 解析参数
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 创建必要目录
    Config.create_directories()
    
    # 执行对应命令
    if args.command == 'collect':
        collect_data(args)
    elif args.command == 'train':
        train_model(args)
    elif args.command == 'fly':
        run_flight(args)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
